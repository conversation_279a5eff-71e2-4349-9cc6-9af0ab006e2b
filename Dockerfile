# 构建阶段
# FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/node:20-alpine3.20-linuxarm64 AS builder
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/node:20.14.0-alpine AS builder

# 设置工作目录
WORKDIR /app

# 创建目录结构
RUN mkdir -p apps/service apps/client libs/client-tsconfig

# 复制所有package.json和yarn.lock文件
# 这样可以利用Docker缓存层，只有package.json变化时才重新安装依赖
COPY package.json yarn.lock ./
COPY turbo.json ./
COPY apps/client/package.json ./apps/client/
COPY apps/service/package.json ./apps/service/
COPY libs/client-tsconfig/package.json ./libs/client-tsconfig/

# 安装依赖
RUN yarn install --frozen-lockfile

# 全局安装NestJS CLI
RUN yarn global add @nestjs/cli

# 复制所有源代码
COPY . .

# 构建项目
RUN yarn build

# 生产阶段
# FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/node:20-alpine3.20-linuxarm64 AS runner
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/node:20.14.0-alpine AS runner

WORKDIR /app

# 复制package.json文件
COPY package.json yarn.lock ./
COPY turbo.json ./
# 设置时区环境变量
ENV TZ=Asia/Shanghai
# 设置生产环境
ENV NODE_ENV=production

# 从构建阶段复制编译后的代码和依赖
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/apps/client/dist ./apps/client/dist
COPY --from=builder /app/apps/service/dist ./apps/service/dist
COPY --from=builder /app/libs ./libs

# 暴露端口
EXPOSE 7001

# 启动应用 - 使用生产模式启动
CMD ["node", "apps/service/dist/main.js"]