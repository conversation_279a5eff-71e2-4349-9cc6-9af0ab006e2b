stages:
  - build:image
  - deploy

variables:
  SERVICE_IMAGE: $HARBOR_REPO/$CI_PROJECT_NAME:$CI_PIPELINE_ID
  NS_TEST: kezhaozhao-test


.base:
  image: harbor-in.greatld.com/kezhaozhao/kzz-gitlab-utils:1.0
  cache: {}
  tags:
    - idc_runner_server_backend
  allow_failure: false
  interruptible: true

.deploy_base:
  image: harbor-in.greatld.com/kezhaozhao/slack-approval-guard:1.2
  extends:
    - .base
  stage: deploy
  except:
    - schedules
  interruptible: true
  before_script:
    - mkdir -p /etc/deploy
    - SERVICE_IMAGE="${SERVICE_IMAGE}-prod"
    - sed -i "s|IMAGE_NAME:IMAGE_TAG|$SERVICE_IMAGE|g" kubernetes/deployment.yaml
    - echo $kube_idc_config |base64 -d > $KUBECONFIG


build_image:
  extends:
    - .base
  allow_failure: false
  stage: build:image
  except:
    - schedules
  only:
    - ci-debug
    - feature-ci
    - /^release-.*$/
    - master
  artifacts:
    name: $DIST_ARTIFICATS_KEY
    expire_in: 3 days
    paths:
      - shared_vars.sh
  script:
    - docker login $HARBOR_URL -u $HARBOR_USER -p $HARBOR_PASSWD
    - echo "inject arg and env to Dockerfile"
    - >
      SERVICE_IMAGE="${SERVICE_IMAGE}-prod";
      cat Dockerfile;
    - time docker build  -t $SERVICE_IMAGE .
    - time docker push $SERVICE_IMAGE

# deploy_idc:
#   extends:
#     - .deploy_base
#   only:
#     - develop
#     - feature-ci
#     - feature-crm-refactor
#   script:
#     - kubectl apply -f kubernetes/deployment.yaml -n $NS_DEV

deploy_test:
  extends:
    - .deploy_base
  only:
    - /^release-.*$/
    - ci-debug
  script:
    - echo $kube_test_config |base64 -d > $KUBECONFIG
    - kubectl apply -f kubernetes/deployment.yaml -n $NS_TEST
    - kubectl apply -f kubernetes/canary.yaml -n $NS_TEST
