# Model Training Platform

A comprehensive platform for training, evaluating, and deploying machine learning models.

## Project Structure

This project is organized as a monorepo using Turborepo, containing:

- `apps/client`: Vue 2.7 frontend with TypeScript and Ant Design Vue
- `apps/service`: NestJS backend with TypeScript and TypeORM
- `libs/client-tsconfig`: Shared TypeScript configurations

## Getting Started

### Prerequisites

- Node.js (v18+)
- Yarn
- Docker (optional, for containerized development)

### Installation

```bash
# Install dependencies
yarn install

# Start development servers
yarn dev

# Or start individual services
yarn dev:client  # Start frontend only
yarn dev:service # Start backend only
```

### Building for Production

```bash
yarn build
```

## Features

- Dataset management
- Training job configuration and monitoring
- Model evaluation and comparison
- Model deployment and versioning
- User access management

## Technology Stack

### Frontend

- Vue 2.7
- TypeScript
- Ant Design Vue 1.7.8
- Pinia for state management
- UnoCSS for styling

### Backend

- NestJS
- TypeScript
- TypeORM
- MySQL
- Redis (for caching and job queues)

## Development

This project follows a monorepo structure using Turborepo for efficient build caching and task running.

### Project Structure

```
├── apps/
│   ├── client/           # Vue frontend
│   └── service/          # NestJS backend
├── libs/
│   └── client-tsconfig/  # Shared TypeScript config
└── package.json          # Root package.json
```

## License

This project is private and not licensed for public use.
