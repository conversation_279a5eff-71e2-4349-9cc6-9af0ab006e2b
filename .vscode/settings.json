{"typescript.tsdk": "node_modules/typescript/lib", "jest.jestCommandLine": "node node_modules/.bin/jest", "jest.autoRun": {"watch": false, "onSave": false}, "jest.pathToJest": "node_modules/.bin/jest", "jest.pathToConfig": "jest.config.js", "jest.nodeEnv": {"NODE_ENV": "test"}, "jest.debugMode": false, "jest.showCoverageOnLoad": false, "jest.autoRun.watch": false, "jestrunner.configPath": "jest.config.js", "npm.packageManager": "yarn", "npm.exclude": "**/.git_projects/**"}