{"name": "model-training-platform-web", "private": true, "version": "0.0.0", "workspaces": ["apps/*", "libs/*"], "scripts": {"postinstall": "patch-package", "dev": "turbo run dev", "build": "turbo run build", "dev:client": "turbo run dev --filter=model-training-platform-client", "dev:service": "turbo run dev --filter=model-training-platform-service", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "devDependencies": {"patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^3.5.3", "turbo": "^2.5.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "dependencies": {"@types/cookie-parser": "^1.4.8", "cookie-parser": "^1.4.7"}}