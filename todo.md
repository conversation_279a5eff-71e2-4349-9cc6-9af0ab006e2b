# 发版管理系统需求完善文档

## 1. 产品发版详情页功能增强

### 1.1 关联项目 -> MR 列表功能

1. **增加 发起MR 按钮 和 发起Hotfix 按钮**

   - 发起MR: 调用 `POST /api/release/project/{id}/patch` 接口
   - 发起Hotfix: 调用 `POST /api/release/hotfix` 接口，需要关联事故编号
   - 按钮状态控制：
     - 项目状态为 `PLANNING` 时显示"发起MR"
     - 项目状态为 `COMPLETED` 时显示"发起Hotfix"
     - 项目状态为 `IN_PROGRESS` 或其他进行中状态时禁用

2. **增加 MR open 的情况下可以增加关闭按钮**

   - 功能：撤销当前MR，相当于取消这次发版尝试
   - 实现：调用GitLab API关闭MR，同时更新项目发版状态
   - 状态更新逻辑：

     ```typescript
     // 更新项目发版状态
     projectRelease.status = ReleaseStatusEnums.CANCELLED;
     projectRelease.errorMessage = '用户手动撤销MR';

     // 刷新产品发版状态
     await this.refreshProductReleaseStatus(productReleaseId);
     ```

   - 需要刷新：
     - 项目发版状态 (`ProjectReleaseEntity`)
     - 产品发版整体状态 (`ProductReleaseEntity`)

## 2. 产品发版核心功能完善

### 2.1 发版类型优化

```typescript
enum ReleaseTypeEnums {
  REGULAR = 'REGULAR', // 常规发版
  HOTFIX = 'HOTFIX', // 热修复发版
  // 移除 TEST 和 OTHER 类型，简化发版流程
}
```

### 2.2 发版环境字段新增

```typescript
// 在 ProjectReleaseEntity 中新增字段
export class ProjectReleaseEntity {
  @Column({ type: 'varchar', name: 'release_environment', default: 'production' })
  @ApiProperty({ description: '发版环境：master对应生产环境，release-current对应测试环境' })
  releaseEnvironment: 'production' | 'testing';

  @Column({ type: 'varchar', name: 'target_branch', default: 'master' })
  @ApiProperty({ description: '目标分支名称' })
  targetBranch: string;
}
```

**环境映射关系：**

- `master` 分支 → 生产环境 (`production`)
- `release-current` 分支 → 测试环境 (`testing`)

### 2.3 项目发版结束状态下的Hotfix逻辑

```typescript
// 新增业务逻辑方法
async createHotfixFromCompletedProject(completedProjectReleaseId: number, incidentNumber: number) {
  const completedProject = await this.getProjectReleaseDetails(completedProjectReleaseId);

  if (completedProject.status !== ReleaseStatusEnums.COMPLETED) {
    throw new Error('只有完成状态的项目才能发起Hotfix');
  }

  // 1. 根据原项目信息创建新的产品发版
  const hotfixProductRelease = ProductReleaseEntity.getInstance(
    completedProject.tbProjectName,
    ReleaseTypeEnums.HOTFIX,
    [completedProject.projectName],
    user.username,
    new Date(), // 立即执行
    ReleaseStatusEnums.PLANNING,
    `Hotfix for incident: ${incidentNumber}`
  );

  // 2. 标记为Hotfix类型并关联原发版
  hotfixProductRelease.refProductReleaseId = completedProject.productReleaseId;

  // 3. 创建对应的项目发版计划
  const hotfixProjectRelease = ProjectReleaseEntity.getInstance(
    completedProject.projectName,
    ReleaseTypeEnums.HOTFIX,
    sourceBranch, // 从请求参数获取
    user.username
  );
  hotfixProjectRelease.incidentNumber = incidentNumber;

  return { hotfixProductRelease, hotfixProjectRelease };
}
```

### 2.4 默认产品配置和自动创建逻辑

#### 2.4.1 默认产品配置

```typescript
// 在 TbProjectDefaultMappings 中扩展依赖关系配置
export const TbProjectDependencyMappings = {
  // 依赖项目：被多个产品线共享的基础服务
  dependencyProjects: [
    'saas-bundle-service', // 基础服务包
    'rover-graph-service', // 图谱服务
    'company-search-api', // 企业搜索API
  ],

  // 工具项目：支撑类项目
  toolProjects: [
    'kzz-data-monitor', // 数据监控
    'dd-platform-service', // 数据平台服务
  ],

  // 产品线依赖关系映射
  productDependencies: {
    Rover: ['saas-bundle-service', 'rover-graph-service', 'company-search-api'],
    Insights: ['rover-graph-service', 'saas-bundle-service', 'company-search-api', 'dd-platform-service'],
    Tender: ['saas-bundle-service', 'qcc-construction-service', 'kzz-data-monitor'],
    CRM: ['kzz-crm-service', 'kzz-enterprise-service', 'kzz-openapi-service'],
  },
};
```

#### 2.4.2 Hotfix自动创建产品发版逻辑

```typescript
async createHotfixProductReleaseForDependency(projectName: string, incidentNumber: number) {
  // 1. 检查该项目是否为依赖项目或工具项目
  const isDependency = TbProjectDependencyMappings.dependencyProjects.includes(projectName);
  const isTool = TbProjectDependencyMappings.toolProjects.includes(projectName);

  if (!isDependency && !isTool) {
    return; // 非共享项目，无需自动创建
  }

  // 2. 找出所有依赖此项目的产品线
  const affectedProducts = [];
  for (const [productName, dependencies] of Object.entries(TbProjectDependencyMappings.productDependencies)) {
    if (dependencies.includes(projectName)) {
      affectedProducts.push(productName);
    }
  }

  // 3. 为每个受影响的产品线自动创建Hotfix产品发版
  const autoCreatedReleases = [];
  for (const productName of affectedProducts) {
    const hotfixRelease = ProductReleaseEntity.getInstance(
      productName as TbProjectEnums,
      ReleaseTypeEnums.HOTFIX,
      [projectName],
      'system-auto', // 系统自动创建
      new Date(),
      ReleaseStatusEnums.PLANNING,
      `Auto-created hotfix for ${projectName} incident: ${incidentNumber}`
    );

    autoCreatedReleases.push(hotfixRelease);
  }

  return autoCreatedReleases;
}
```

### 2.5 创建产品发版时的多产品线检查和关联

#### 2.5.1 多产品线归属检查

```typescript
async createProductReleaseWithDependencyCheck(createRequest: CreateProductReleaseRequest) {
  const { relatedProjects, tbProjectName } = createRequest;

  // 检查每个项目是否归属多个产品线
  const crossProductProjects = [];

  for (const project of relatedProjects) {
    const belongingProducts = getTbProjectNameList(project.projectName);

    if (belongingProducts.length > 1) {
      crossProductProjects.push({
        projectName: project.projectName,
        belongingProducts,
        currentProduct: tbProjectName
      });
    }
  }

  // 为跨产品线项目创建关联发版
  const relatedProductReleases = [];

  for (const crossProject of crossProductProjects) {
    const otherProducts = crossProject.belongingProducts.filter(p => p !== tbProjectName);

    for (const otherProduct of otherProducts) {
      const relatedRelease = ProductReleaseEntity.getInstance(
        otherProduct,
        createRequest.releaseType,
        [crossProject.projectName],
        createRequest.createdBy,
        new Date(createRequest.plannedReleaseTime),
        ReleaseStatusEnums.PLANNING,
        `Auto-created for cross-product project: ${crossProject.projectName}`
      );

      // 设置引用关系
      relatedRelease.refProductReleaseId = mainProductRelease.id;
      relatedProductReleases.push(relatedRelease);
    }
  }

  return {
    mainProductRelease,
    relatedProductReleases,
    crossProductProjects
  };
}
```

#### 2.5.2 产品发版实体字段扩展

```typescript
export class ProductReleaseEntity {
  // ... 现有字段 ...

  @Column({ type: 'int', nullable: true, name: 'ref_product_release_id' })
  @ApiProperty({
    description: '关联的主产品发版ID，用于标识由其他产品发版自动创建的关联发版',
    required: false,
  })
  refProductReleaseId?: number;

  @Column({ type: 'varchar', name: 'release_environment', default: 'production' })
  @ApiProperty({
    description: '发版环境：production(生产环境)或testing(测试环境)',
    enum: ['production', 'testing'],
    default: 'production',
  })
  releaseEnvironment: 'production' | 'testing';

  @Column({ type: 'boolean', name: 'is_auto_created', default: false })
  @ApiProperty({
    description: '是否为系统自动创建的关联发版',
    default: false,
  })
  isAutoCreated: boolean;
}
```

## 3. 业务流程图

### 3.1 标准产品发版流程

```mermaid
graph TD
    A[创建产品发版] --> B{检查依赖项目}
    B -->|有跨产品线项目| C[自动创建关联产品发版]
    B -->|无跨产品线项目| D[创建项目发版计划]
    C --> D
    D --> E[启动产品发版]
    E --> F[创建MR并开始CI/CD]
    F --> G{发版环境测试}
    G -->|测试失败| H[项目补丁修复]
    H --> F
    G -->|测试通过| I[生产环境发版]
    I --> J[发版完成]
```

### 3.2 Hotfix发版流程

```mermaid
graph TD
    A[生产事故发生] --> B[创建事故记录]
    B --> C{项目状态检查}
    C -->|项目已完成| D[创建Hotfix产品发版]
    C -->|项目进行中| E[项目补丁修复]
    D --> F{检查是否为依赖项目}
    F -->|是依赖项目| G[自动为关联产品线创建Hotfix]
    F -->|非依赖项目| H[创建单独Hotfix项目发版]
    G --> H
    H --> I[启动Hotfix发版流程]
    I --> J[快速审批和部署]
    J --> K[验证修复效果]
    K --> L[更新事故状态]
```

## 4. API接口扩展需求

### 4.1 新增接口

```typescript
// 关闭MR接口
@Post('product/:id/project/:projectId/close-mr')
async closeMergeRequest(@Param('id') productId: number, @Param('projectId') projectId: number);

// 检查项目多产品线归属
@Get('project/:projectName/cross-product-check')
async checkCrossProductDependency(@Param('projectName') projectName: string);

// 为依赖项目创建Hotfix
@Post('dependency/:projectName/auto-hotfix')
async createAutoHotfixForDependency(@Param('projectName') projectName: string, @Body() request: CreateAutoHotfixRequest);
```

### 4.2 响应模型扩展

```typescript
export class ProductReleaseDetailResponse extends ProductReleaseEntity {
  @ApiProperty({ description: '关联的产品发版列表' })
  relatedProductReleases?: ProductReleaseEntity[];

  @ApiProperty({ description: '跨产品线项目信息' })
  crossProductProjects?: CrossProductProjectInfo[];

  @ApiProperty({ description: '是否可以发起Hotfix' })
  canCreateHotfix: boolean;

  @ApiProperty({ description: '环境部署状态' })
  environmentStatus: {
    testing: 'pending' | 'deployed' | 'failed';
    production: 'pending' | 'deployed' | 'failed';
  };
}
```

## 5. 前端页面交互优化

### 5.1 产品发版详情页

- **状态指示器**：清晰显示测试环境和生产环境的部署状态
- **操作按钮**：根据项目状态动态显示"发起MR"、"发起Hotfix"、"关闭MR"按钮
- **关联发版展示**：显示由当前发版自动创建的关联产品发版
- **实时状态更新**：通过WebSocket或轮询实时更新发版进度

### 5.2 MR列表增强

- **MR状态标识**：Open/Merged/Closed状态可视化
- **操作历史**：显示每次补丁修复的尝试记录
- **快速操作**：一键关闭MR、重新发起等

### 5.3 Hotfix创建流程

- **事故关联**：必须关联事故编号，支持从事故系统选择
- **影响范围展示**：显示Hotfix将影响的所有产品线
- **快速审批**：简化Hotfix的审批流程，支持紧急发版

这个完善后的文档涵盖了当前发版管理系统的核心功能增强，结合了现有代码架构，提供了详细的实现方案和业务流程。
