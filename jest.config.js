module.exports = {
  projects: [
    {
      displayName: 'service',
      testMatch: ['<rootDir>/apps/service/src/**/*.spec.ts', '<rootDir>/apps/service/src/**/*.test.ts'],
      moduleFileExtensions: ['js', 'json', 'ts'],
      transform: {
        '^.+\\.(t|j)s$': [
          'ts-jest',
          {
            tsconfig: '<rootDir>/apps/service/tsconfig.json',
            isolatedModules: true,
          },
        ],
      },
      moduleNameMapper: {
        '^src/(.*)$': '<rootDir>/apps/service/src/$1',
      },
      testEnvironment: 'node',
      roots: ['<rootDir>/apps/service/src'],
      modulePaths: ['<rootDir>/apps/service/src'],
      testPathIgnorePatterns: ['/node_modules/'],
      transformIgnorePatterns: ['/node_modules/'],
      globals: {
        'ts-jest': {
          tsconfig: '<rootDir>/apps/service/tsconfig.json',
          isolatedModules: true,
        },
      },
      setupFilesAfterEnv: ['<rootDir>/apps/service/jest.setup.js'],
      verbose: true,
      testTimeout: 30000,
    },
    // 可以在这里添加其他项目的配置
  ],
  collectCoverageFrom: ['**/*.(t|j)s'],
  coverageDirectory: 'coverage',
  verbose: true,
  testTimeout: 30000,
  // 添加以下配置以支持 VSCode Jest 插件
  rootDir: '.',
  testEnvironment: 'node',
  moduleDirectories: ['node_modules', 'src'],
  testPathIgnorePatterns: ['/node_modules/'],
  transformIgnorePatterns: ['/node_modules/'],
};
