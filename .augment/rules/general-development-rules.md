---
type: 'manual'
---

# 通用开发规范

适用于模型训练平台项目的通用开发规范和最佳实践。

## 🎯 核心原则

### 代码质量原则

- **可读性优先**: 代码应该易于理解和维护
- **简洁明了**: 避免过度设计，倾向于直接的解决方案
- **类型安全**: 充分利用TypeScript的类型系统
- **一致性**: 遵循项目既定的代码风格和约定
- **可测试性**: 编写易于测试的代码

### 开发流程原则

- **增量开发**: 小步快跑，频繁集成
- **代码审查**: 所有代码变更都应经过审查
- **文档同步**: 代码变更时同步更新文档
- **测试驱动**: 重要功能应有相应的测试覆盖

## 📝 代码风格规范

### 命名约定

```typescript
// 文件命名: kebab-case
user - profile.component.ts;
dataset - service.ts;
training - job.entity.ts;

// 类名: PascalCase
class UserProfileComponent {}
class DatasetService {}
class TrainingJobEntity {}

// 变量和函数: camelCase
const userName = 'admin';
const isLoggedIn = true;
function getUserProfile() {}
async function createDataset() {}

// 常量: UPPER_SNAKE_CASE
const API_BASE_URL = '/api';
const MAX_FILE_SIZE = 1024 * 1024;

// 枚举: PascalCase
enum ModelType {
  INNOVATION_HEALTH = 'innovation_health',
  RISK_CONTROL = 'risk_control',
}
```

### 函数设计规范

```typescript
// ✅ 好的函数设计
// 1. 单一职责
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 2. 纯函数（无副作用）
function calculateFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// 3. 明确的参数和返回类型
async function fetchDataset(id: string): Promise<Dataset | null> {
  try {
    const response = await datasetService.getById(id);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch dataset:', error);
    return null;
  }
}

// ❌ 避免的函数设计
// 1. 函数过长
// 2. 多个职责
// 3. 副作用不明确
// 4. 缺少类型定义
```

### 错误处理规范

```typescript
// ✅ 统一的错误处理
class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 服务层错误处理
async function createDataset(data: CreateDatasetRequest): Promise<Dataset> {
  try {
    const response = await httpClient.post('/api/datasets', data);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400) {
      throw new ApiError('请求参数错误', 400, 'INVALID_PARAMS');
    }
    if (error.response?.status === 401) {
      throw new ApiError('未授权访问', 401, 'UNAUTHORIZED');
    }
    throw new ApiError('创建数据集失败', 500, 'CREATE_FAILED');
  }
}

// 组件层错误处理
const handleCreateDataset = async () => {
  try {
    loading.value = true;
    await datasetStore.createDataset(formData);
    message.success('数据集创建成功');
    router.push('/datasets');
  } catch (error) {
    if (error instanceof ApiError) {
      message.error(error.message);
    } else {
      message.error('操作失败，请稍后重试');
    }
  } finally {
    loading.value = false;
  }
};
```

## 🔧 TypeScript 最佳实践

### 类型定义

```typescript
// ✅ 明确的接口定义
interface Dataset {
  id: string;
  name: string;
  description: string;
  modelType: ModelType;
  status: DatasetStatus;
  createdAt: string;
  updatedAt: string;
  metadata?: DatasetMetadata;
}

// ✅ 使用联合类型
type DatasetStatus = 'draft' | 'processing' | 'ready' | 'error';

// ✅ 泛型使用
interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

// ✅ 工具类型使用
type CreateDatasetRequest = Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>;
type UpdateDatasetRequest = Partial<Pick<Dataset, 'name' | 'description'>>;

// ❌ 避免使用 any
// any 类型会失去类型检查的优势
```

### 类型守卫

```typescript
// 类型守卫函数
function isDataset(obj: unknown): obj is Dataset {
  return typeof obj === 'object' && obj !== null && 'id' in obj && 'name' in obj && 'modelType' in obj;
}

// 使用类型守卫
function processApiResponse(response: unknown) {
  if (isDataset(response)) {
    // 这里 response 被正确推断为 Dataset 类型
    console.log(response.name);
  }
}
```

## 📊 性能优化规范

### 前端性能优化

```typescript
// ✅ 组件懒加载
const DatasetManagement = () => import('@/pages/datasets');

// ✅ 计算属性缓存
const filteredDatasets = computed(() => {
  return datasets.value.filter((dataset) => dataset.name.toLowerCase().includes(searchTerm.value.toLowerCase()));
});

// ✅ 防抖处理
import { debounce } from 'lodash-es';

const debouncedSearch = debounce((term: string) => {
  searchDatasets(term);
}, 300);

// ✅ 虚拟滚动（大列表）
// 使用 ant-design-vue 的虚拟滚动组件
```

### 后端性能优化

```typescript
// ✅ 数据库查询优化
@Entity('datasets')
export class DatasetEntity {
  // 添加索引
  @Index()
  @Column()
  modelType: string;

  @Index()
  @Column()
  status: string;
}

// ✅ 分页查
async function findDatasets(page: number = 1, limit: number = 20, filters?: DatasetFilters): Promise<PaginatedResult<Dataset>> {
  const queryBuilder = this.datasetRepository
    .createQueryBuilder('dataset')
    .skip((page - 1) * limit)
    .take(limit);

  if (filters?.modelType) {
    queryBuilder.andWhere('dataset.modelType = :modelType', {
      modelType: filters.modelType,
    });
  }

  const [items, total] = await queryBuilder.getManyAndCount();

  return {
    items,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
}
```

## 🔒 安全规范

### 输入验证

```typescript
// ✅ 后端输入验证
import { IsString, IsNotEmpty, Length, IsEnum } from 'class-validator';

export class CreateDatasetDto {
  @IsString({ message: '名称必须是字符串' })
  @IsNotEmpty({ message: '名称不能为空' })
  @Length(1, 100, { message: '名称长度必须在1-100字符之间' })
  name: string;

  @IsEnum(ModelType, { message: '无效的模型类型' })
  modelType: ModelType;
}

// ✅ 前端输入验证
const validateForm = () => {
  const errors: string[] = [];

  if (!form.name.trim()) {
    errors.push('名称不能为空');
  }

  if (form.name.length > 100) {
    errors.push('名称长度不能超过100字符');
  }

  return errors;
};
```

### 权限控制

```typescript
// ✅ 基于角色的权限控制
enum Permission {
  READ_DATASET = 'read:dataset',
  WRITE_DATASET = 'write:dataset',
  DELETE_DATASET = 'delete:dataset',
  MANAGE_USERS = 'manage:users',
}

// 权限装饰器
@RequirePermissions(Permission.WRITE_DATASET)
@Post('datasets')
async createDataset(@Body() dto: CreateDatasetDto) {
  return this.datasetsService.create(dto);
}
```

## 📝 文档规范

### 代码注释

```typescript
/**
 * 数据集服务类
 * 负责数据集的CRUD操作和业务逻辑处理
 */
export class DatasetService {
  /**
   * 创建新的数据集
   * @param createDto 创建数据集的请求数据
   * @returns 创建成功的数据集实体
   * @throws {BadRequestException} 当请求参数无效时
   * @throws {ConflictException} 当数据集名称已存在时
   */
  async create(createDto: CreateDatasetDto): Promise<Dataset> {
    // 实现逻辑
  }
}
```

### API 文档

```typescript
// ✅ 完整的 Swagger 文档
@ApiTags('数据集管理')
@Controller('api/datasets')
export class DatasetsController {
  @Post()
  @ApiOperation({
    summary: '创建数据集',
    description: '创建一个新的数据集，支持多种模型类型',
  })
  @ApiBody({ type: CreateDatasetDto })
  @ApiResponse({
    status: 201,
    description: '数据集创建成功',
    type: DatasetEntity,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  async create(@Body() createDto: CreateDatasetDto) {
    return this.datasetsService.create(createDto);
  }
}
```

## 🧪 测试规范

### 测试分层

```typescript
// 单元测试 - 测试单个函数/方法
describe('DatasetService', () => {
  describe('create', () => {
    it('should create dataset successfully', async () => {
      // Arrange
      const createDto = { name: 'Test Dataset', modelType: ModelType.INNOVATION_HEALTH };
      const expectedDataset = { id: '1', ...createDto };

      // Act
      const result = await service.create(createDto);

      // Assert
      expect(result).toEqual(expectedDataset);
    });
  });
});

// 集成测试 - 测试API端点
describe('DatasetsController (e2e)', () => {
  it('/datasets (POST)', () => {
    return request(app.getHttpServer())
      .post('/api/datasets')
      .send({ name: 'Test Dataset', modelType: 'innovation_health' })
      .expect(201)
      .expect((res) => {
        expect(res.body.name).toBe('Test Dataset');
      });
  });
});
```

## 🔄 版本控制规范

### Git 提交规范

```bash
# 提交消息格式
<type>(<scope>): <description>

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(datasets): add dataset creation functionality
fix(auth): resolve login session timeout issue
docs(api): update swagger documentation
```

### 分支管理

```bash
# 主分支
main/master - 生产环境代码

# 开发分支
develop - 开发环境代码

# 功能分支
feature/dataset-management
feature/user-authentication

# 修复分支
hotfix/login-bug
hotfix/security-patch
```
