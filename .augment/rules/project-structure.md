# 模型训练平台项目结构与代码规范

你是一个专业的全栈开发专家，熟悉Vue.js、TypeScript和NestJS。

本项目为模型训练平台，采用monorepo结构，包含前端、后端和共享库，使用TypeScript为主语言。

## 🎯 通用原则 (始终遵循)

请遵循以下通用原则：
- 使用中文进行用户交互和代码注释
- 遵循项目既定的目录结构和命名约定
- 代码生成要符合项目的技术栈和规范
- 优先考虑代码的可读性、可维护性和性能
- 使用有意义的变量名和函数名
- 避免过度设计，倾向于直截了当的解决方案

## 📁 主要目录结构

```
model-training-platform-web/
├── apps/                          # 应用主目录
│   ├── client/                    # 前端项目 (Vue 2.7+TypeScript+Ant Design Vue)
│   │   ├── src/
│   │   │   ├── pages/             # 页面组件
│   │   │   ├── components/        # 可复用组件
│   │   │   ├── layouts/           # 布局组件
│   │   │   ├── router/            # 路由配置
│   │   │   ├── store/             # Pinia状态管理
│   │   │   ├── core/              # 核心服务和实体
│   │   │   │   ├── services/      # API服务
│   │   │   │   └── entities/      # 数据实体
│   │   │   ├── utils/             # 工具函数
│   │   │   └── assets/            # 静态资源
│   │   ├── vite.config.ts         # Vite配置
│   │   └── package.json
│   └── service/                   # 后端项目 (NestJS+TypeScript+TypeORM)
│       ├── src/
│       │   ├── auth/              # 认证模块
│       │   ├── datasets/          # 数据集管理模块
│       │   ├── training/          # 训练任务模块
│       │   ├── entities/          # TypeORM实体
│       │   ├── common/            # 通用模块
│       │   ├── config/            # 配置模块
│       │   └── main.ts            # 应用入口
│       └── package.json
├── libs/                          # 共享库
│   └── client-tsconfig/           # 共享TypeScript配置
├── .augment/                      # Augment配置
│   └── rules/                     # Augment规则文件
├── turbo.json                     # Turborepo配置
└── package.json                   # 根package.json
```

## 🎨 前端技术栈 (apps/client/)

### 核心技术
- **框架**: Vue 2.7 + TypeScript
- **UI库**: Ant Design Vue 1.7.8
- **路由**: Vue Router 3.x
- **状态管理**: Pinia
- **构建工具**: Vite
- **样式**: UnoCSS + Less

### 目录规范
- `pages/`: 页面组件，按业务模块组织
- `components/`: 可复用组件
- `layouts/`: 布局组件 (default.layout, workbench.layout)
- `core/services/`: API服务层
- `store/`: Pinia状态管理
- `router/`: 路由配置和守卫

## 🔧 后端技术栈 (apps/service/)

### 核心技术
- **框架**: NestJS + TypeScript
- **ORM**: TypeORM
- **数据库**: MySQL
- **缓存**: Redis
- **认证**: Session-based认证

### 模块结构
- `auth/`: 认证和授权模块
- `datasets/`: 数据集管理模块
- `training/`: 训练任务模块
- `entities/`: 数据库实体定义
- `common/`: 通用DTO、枚举等
- `config/`: 应用配置

## 🚀 主要功能模块

### 1. 数据集管理
- 数据集上传和存储
- 数据集元数据管理
- 支持多种模型类型的数据集

### 2. 训练任务管理
- 训练任务配置和启动
- 训练进度监控
- 训练结果管理

### 3. 模型管理
- 模型版本控制
- 模型评估和比较
- 模型部署管理

### 4. 用户管理
- 用户认证和授权
- 角色权限管理
- 会话管理

## 📝 开发规范

### 命名约定
- 文件名: kebab-case (如: user-profile.vue)
- 组件名: PascalCase (如: UserProfile)
- 变量/函数: camelCase (如: getUserInfo)
- 常量: UPPER_SNAKE_CASE (如: API_BASE_URL)
- 类名: PascalCase (如: UserService)

### 代码组织
- 每个文件单一职责
- 函数保持简短，单一功能
- 使用TypeScript类型定义
- 添加适当的注释和文档

### API设计
- RESTful风格
- 统一的响应格式
- 适当的HTTP状态码
- 完整的Swagger文档

## 🔒 安全规范

- 使用Session认证机制
- 输入验证和数据校验
- SQL注入防护
- XSS防护
- CSRF防护

## 🧪 测试规范

- 单元测试覆盖核心业务逻辑
- 集成测试覆盖API接口
- 前端组件测试
- E2E测试覆盖关键用户流程
