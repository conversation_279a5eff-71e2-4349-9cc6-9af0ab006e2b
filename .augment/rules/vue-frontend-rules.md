# Vue 前端开发规范

基于 Vue 2.7 + TypeScript + Ant Design Vue 1.7.8 的前端开发规范。

## 🎯 技术栈

- **框架**: Vue 2.7 + TypeScript
- **UI库**: Ant Design Vue 1.7.8
- **路由**: Vue Router 3.x
- **状态管理**: Pinia
- **构建工具**: Vite
- **样式**: UnoCSS + Less

## 📁 目录结构规范

```
src/
├── pages/                         # 页面组件
│   ├── dashboard/                 # 仪表板页面
│   ├── datasets/                  # 数据集管理页面
│   ├── training/                  # 训练任务页面
│   ├── models/                    # 模型管理页面
│   ├── deployment/                # 部署管理页面
│   ├── monitoring/                # 系统监控页面
│   ├── login/                     # 登录页面
│   └── not-found/                 # 404页面
├── components/                    # 可复用组件
│   ├── common/                    # 通用组件
│   └── business/                  # 业务组件
├── layouts/                       # 布局组件
│   ├── default.layout/            # 默认布局(登录页等)
│   └── workbench.layout/          # 工作台布局(主要页面)
├── core/                          # 核心模块
│   ├── services/                  # API服务
│   │   ├── http-client.ts         # HTTP客户端
│   │   ├── api.service.ts         # API服务定义
│   │   └── index.ts               # 服务导出
│   └── entities/                  # 数据实体类型定义
├── store/                         # Pinia状态管理
│   ├── modules/                   # 状态模块
│   │   ├── user.store.ts          # 用户状态
│   │   ├── dataset.store.ts       # 数据集状态
│   │   └── training.store.ts      # 训练任务状态
│   └── index.ts                   # Store导出
├── router/                        # 路由配置
│   └── index.ts                   # 路由定义和守卫
├── utils/                         # 工具函数
├── assets/                        # 静态资源
│   ├── images/                    # 图片资源
│   ├── styles/                    # 样式文件
│   └── main.less                  # 主样式文件
├── app/                           # 应用根组件
│   └── index.tsx                  # App组件
└── main.ts                        # 应用入口
```

## 🎨 组件开发规范

### 组件定义
```typescript
// 使用 defineComponent 和 TSX 语法
import { defineComponent, ref, reactive } from 'vue';

export default defineComponent({
  name: 'ComponentName',
  props: {
    // 定义props类型
  },
  setup(props, { emit }) {
    // 组件逻辑
    return () => (
      // TSX模板
    );
  },
});
```

### 页面组件结构
```typescript
// pages/example/index.tsx
import { defineComponent, ref, onMounted } from 'vue';
import { useExampleStore } from '@/store/modules/example.store';

export default defineComponent({
  name: 'ExamplePage',
  setup() {
    const store = useExampleStore();
    const loading = ref(false);

    const handleAction = async () => {
      // 处理逻辑
    };

    onMounted(() => {
      // 初始化逻辑
    });

    return () => (
      <div class="example-page">
        {/* 页面内容 */}
      </div>
    );
  },
});
```

## 🔄 状态管理规范

### Pinia Store 结构
```typescript
// store/modules/example.store.ts
import { defineStore } from 'pinia';
import { exampleService } from '@/core/services';

interface ExampleState {
  items: ExampleItem[];
  loading: boolean;
  error: string | null;
}

export const useExampleStore = defineStore('example', {
  state: (): ExampleState => ({
    items: [],
    loading: false,
    error: null,
  }),

  getters: {
    // 计算属性
  },

  actions: {
    async fetchItems() {
      this.loading = true;
      try {
        this.items = await exampleService.getItems();
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },
  },
});
```

## 🌐 API服务规范

### 服务类定义
```typescript
// core/services/api.service.ts
import { BaseService } from './base.service';
import type { HttpClient } from './http-client';

export class ExampleService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly GET_ITEMS = '/api/example/items';
  static readonly CREATE_ITEM = '/api/example/create';

  getItems = () => {
    return this.httpClient.get(ExampleService.GET_ITEMS);
  };

  createItem = (data: CreateItemRequest) => {
    return this.httpClient.post(ExampleService.CREATE_ITEM, data);
  };
}
```

## 🎭 UI组件使用规范

### Ant Design Vue 组件
```typescript
// 使用 a- 前缀的组件
return () => (
  <div>
    <a-form model={form} onFinish={handleSubmit}>
      <a-form-item label="名称" name="name">
        <a-input v-model={form.name} placeholder="请输入名称" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" htmlType="submit" loading={loading}>
          提交
        </a-button>
      </a-form-item>
    </a-form>
  </div>
);
```

## 🛣️ 路由配置规范

### 路由定义
```typescript
// router/index.ts
const routes = [
  {
    path: '/dashboard',
    component: WorkbenchLayout,
    children: [
      {
        path: '',
        name: 'dashboard',
        meta: {
          title: '仪表板',
          icon: 'dashboard',
        },
        component: () => import('@/pages/dashboard'),
      },
    ],
  },
];
```

### 路由守卫
```typescript
// 认证守卫
router.beforeEach((to, from, next) => {
  if (to.matched.some(record => record.meta.noAuth)) {
    return next();
  }
  
  const user = localStorage.getItem('user');
  if (!user) {
    return next({ path: '/login', query: { redirect: to.fullPath } });
  }
  
  return next();
});
```

## 🎨 样式规范

### CSS类命名
- 使用 kebab-case
- 页面级样式使用页面名作为前缀
- 组件级样式使用组件名作为前缀

### Less样式组织
```less
// 页面样式
.dashboard-page {
  padding: 24px;
  
  .header {
    margin-bottom: 24px;
  }
  
  .content {
    background: #fff;
    border-radius: 6px;
  }
}
```

## 🔧 工具函数规范

### 工具函数组织
```typescript
// utils/format.ts
export const formatDate = (date: Date): string => {
  // 格式化日期
};

export const formatFileSize = (size: number): string => {
  // 格式化文件大小
};
```

## 📝 类型定义规范

### 接口定义
```typescript
// core/entities/index.ts
export interface Dataset {
  id: string;
  name: string;
  description: string;
  modelType: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDatasetRequest {
  name: string;
  description: string;
  modelType: string;
}
```

## 🧪 测试规范

### 组件测试
```typescript
// 使用 Vitest 进行组件测试
import { mount } from '@vue/test-utils';
import ExampleComponent from '@/components/ExampleComponent.vue';

describe('ExampleComponent', () => {
  it('should render correctly', () => {
    const wrapper = mount(ExampleComponent);
    expect(wrapper.exists()).toBe(true);
  });
});
```

## 📋 代码质量

### ESLint 规则
- 使用项目配置的 ESLint 规则
- 保持代码格式一致性
- 避免使用 any 类型
- 优先使用 const，避免 var

### 性能优化
- 合理使用 computed 和 watch
- 避免在模板中使用复杂表达式
- 使用 v-show 和 v-if 的场景区分
- 合理使用组件懒加载
