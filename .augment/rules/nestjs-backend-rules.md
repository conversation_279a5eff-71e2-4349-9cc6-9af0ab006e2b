# NestJS 后端开发规范

基于 NestJS + TypeScript + TypeORM 的后端开发规范。

## 🎯 技术栈

- **框架**: NestJS + TypeScript
- **ORM**: TypeORM
- **数据库**: MySQL
- **缓存**: Redis
- **认证**: Session-based认证
- **文档**: Swagger

## 📁 目录结构规范

```
src/
├── auth/                          # 认证模块
│   ├── controllers/               # 认证控制器
│   ├── services/                  # 认证服务
│   ├── guards/                    # 认证守卫
│   ├── dto/                       # 认证DTO
│   └── auth.module.ts             # 认证模块
├── datasets/                      # 数据集管理模块
│   ├── controllers/               # 数据集控制器
│   ├── services/                  # 数据集服务
│   ├── dto/                       # 数据集DTO
│   └── datasets.module.ts         # 数据集模块
├── training/                      # 训练任务模块
│   ├── controllers/               # 训练控制器
│   ├── services/                  # 训练服务
│   ├── dto/                       # 训练DTO
│   └── training.module.ts         # 训练模块
├── entities/                      # TypeORM实体
│   ├── dataset.entity.ts          # 数据集实体
│   ├── training.entity.ts         # 训练任务实体
│   └── user.entity.ts             # 用户实体
├── common/                        # 通用模块
│   ├── dto/                       # 通用DTO
│   ├── enums/                     # 枚举定义
│   ├── filters/                   # 异常过滤器
│   ├── guards/                    # 通用守卫
│   ├── interceptors/              # 拦截器
│   └── pipes/                     # 管道
├── config/                        # 配置模块
│   ├── configuration.ts           # 应用配置
│   └── database.config.ts         # 数据库配置
├── app/                           # 应用模块
│   ├── app.controller.ts          # 应用控制器
│   ├── app.service.ts             # 应用服务
│   └── app.module.ts              # 应用模块
└── main.ts                        # 应用入口
```

## 🎯 基本原则

### TypeScript 规范
- 始终声明变量和函数的类型
- 避免使用 any 类型
- 创建必要的类型定义
- 使用 JSDoc 文档化公共类和方法
- 函数内不留空行
- 每个文件一个导出

### 命名规范
- 类名使用 PascalCase
- 变量、函数、方法使用 camelCase
- 文件和目录名使用 kebab-case
- 环境变量使用 UPPERCASE
- 避免魔法数字，定义常量
- 函数以动词开头
- 布尔变量使用 is/has/can 等前缀

## 🏗️ 模块化架构

### 模块结构
```typescript
// example.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExampleController } from './controllers/example.controller';
import { ExampleService } from './services/example.service';
import { ExampleEntity } from '../entities/example.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ExampleEntity])],
  controllers: [ExampleController],
  providers: [ExampleService],
  exports: [ExampleService],
})
export class ExampleModule {}
```

### 控制器规范
```typescript
// controllers/example.controller.ts
import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ExampleService } from '../services/example.service';
import { CreateExampleDto } from '../dto/create-example.dto';
import { CustomAuthGuard } from '../../auth/guards/custom-auth.guard';

@ApiTags('示例管理')
@Controller('api/example')
@UseGuards(CustomAuthGuard)
export class ExampleController {
  constructor(private readonly exampleService: ExampleService) {}

  @Post()
  @ApiOperation({
    summary: '创建示例',
    description: '创建一个新的示例记录',
  })
  @ApiResponse({ status: 201, description: '创建成功' })
  async create(@Body() createExampleDto: CreateExampleDto) {
    return this.exampleService.create(createExampleDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取示例详情' })
  async findOne(@Param('id') id: string) {
    return this.exampleService.findOne(id);
  }
}
```

### 服务层规范
```typescript
// services/example.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExampleEntity } from '../../entities/example.entity';
import { CreateExampleDto } from '../dto/create-example.dto';

@Injectable()
export class ExampleService {
  constructor(
    @InjectRepository(ExampleEntity)
    private readonly exampleRepository: Repository<ExampleEntity>,
  ) {}

  async create(createExampleDto: CreateExampleDto): Promise<ExampleEntity> {
    this.validateBusinessRules(createExampleDto);
    
    const example = new ExampleEntity();
    this.setBasicInfo(example, createExampleDto);
    
    return this.exampleRepository.save(example);
  }

  async findOne(id: string): Promise<ExampleEntity> {
    const example = await this.exampleRepository.findOne({ where: { id } });
    if (!example) {
      throw new NotFoundException(`Example with ID ${id} not found`);
    }
    return example;
  }

  private validateBusinessRules(data: CreateExampleDto): void {
    // 业务规则验证
  }

  private setBasicInfo(example: ExampleEntity, dto: CreateExampleDto): void {
    // 设置基本信息
  }
}
```

## 📊 数据层规范

### 实体定义
```typescript
// entities/example.entity.ts
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('examples')
export class ExampleEntity {
  @ApiProperty({ description: '主键ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '名称' })
  @Column({ length: 100, comment: '名称' })
  name: string;

  @ApiProperty({ description: '描述' })
  @Column({ type: 'text', nullable: true, comment: '描述' })
  description: string;

  @ApiProperty({ description: '状态' })
  @Column({ 
    type: 'enum', 
    enum: ['active', 'inactive'], 
    default: 'active',
    comment: '状态'
  })
  status: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
```

### DTO 定义
```typescript
// dto/create-example.dto.ts
import { IsString, IsNotEmpty, Length, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateExampleDto {
  @ApiProperty({ description: '名称', example: '示例名称' })
  @IsString({ message: '名称必须是字符串' })
  @IsNotEmpty({ message: '名称不能为空' })
  @Length(1, 100, { message: '名称长度必须在1-100字符之间' })
  name: string;

  @ApiProperty({ description: '描述', required: false })
  @IsString({ message: '描述必须是字符串' })
  @IsOptional()
  description?: string;
}
```

## 🔐 认证和授权

### 认证守卫
```typescript
// guards/custom-auth.guard.ts
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RedisService } from '../services/redis.service';

@Injectable()
export class CustomAuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private redisService: RedisService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.get<boolean>('isPublic', context.getHandler());
    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const sessionId = request.cookies['MTSESSIONID'];
    
    if (!sessionId) {
      throw new UnauthorizedException('未登录');
    }

    const user = await this.getUserFromSession(sessionId);
    if (!user) {
      throw new UnauthorizedException('会话已过期');
    }

    request.user = user;
    return true;
  }

  private async getUserFromSession(sessionId: string) {
    // 从Redis获取用户信息
  }
}
```

## 📝 API 文档规范

### Swagger 注解
```typescript
@ApiTags('数据集管理')
@Controller('api/datasets')
export class DatasetsController {
  @Post()
  @ApiOperation({
    summary: '创建数据集',
    description: '创建一个新的数据集，支持多种模型类型',
  })
  @ApiResponse({ 
    status: 201, 
    description: '创建成功',
    type: DatasetEntity 
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误' 
  })
  async create(@Body() createDatasetDto: CreateDatasetDto) {
    return this.datasetsService.create(createDatasetDto);
  }
}
```

## 🔧 配置管理

### 配置文件
```typescript
// config/configuration.ts
export default () => ({
  port: parseInt(process.env.PORT, 10) || 7001,
  database: {
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 3306,
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'model_training_platform',
    synchronize: process.env.NODE_ENV !== 'production',
    logging: process.env.NODE_ENV === 'development',
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD || '',
  },
});
```

## 🚨 异常处理

### 全局异常过滤器
```typescript
// filters/global-exception.filter.ts
import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    const status = exception instanceof HttpException 
      ? exception.getStatus() 
      : HttpStatus.INTERNAL_SERVER_ERROR;

    const message = exception instanceof HttpException 
      ? exception.getResponse() 
      : '服务器内部错误';

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message,
    });
  }
}
```

## 🧪 测试规范

### 单元测试
```typescript
// example.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExampleService } from './example.service';
import { ExampleEntity } from '../entities/example.entity';

describe('ExampleService', () => {
  let service: ExampleService;
  let repository: Repository<ExampleEntity>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExampleService,
        {
          provide: getRepositoryToken(ExampleEntity),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<ExampleService>(ExampleService);
    repository = module.get<Repository<ExampleEntity>>(getRepositoryToken(ExampleEntity));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
```

## 📋 代码质量

### 函数设计
- 编写短函数，单一目的，少于20行
- 函数名以动词开头
- 避免嵌套块，使用早期检查和返回
- 使用高阶函数避免嵌套
- 使用默认参数值而不是检查null或undefined

### 类设计
- 遵循SOLID原则
- 优先组合而非继承
- 声明接口定义契约
- 编写小类，单一目的
- 少于200行代码，少于10个公共方法

### 数据处理
- 避免滥用原始类型，使用复合类型封装数据
- 在函数中避免数据验证，使用带内部验证的类
- 优先使用不可变数据
- 对不变数据使用readonly
