diff --git a/node_modules/ant-design-vue/es/_util/moment-util.js b/node_modules/ant-design-vue/es/_util/moment-util.js
index 2a1c223..2f53c70 100644
--- a/node_modules/ant-design-vue/es/_util/moment-util.js
+++ b/node_modules/ant-design-vue/es/_util/moment-util.js
@@ -1,5 +1,5 @@
 import interopDefault from './interopDefault';
-import * as moment from 'moment';
+import moment from 'moment';
 import warning from './warning';
 import isNil from 'lodash/isNil';
 
diff --git a/node_modules/ant-design-vue/es/calendar/index.js b/node_modules/ant-design-vue/es/calendar/index.js
index 9e24e67..d046764 100644
--- a/node_modules/ant-design-vue/es/calendar/index.js
+++ b/node_modules/ant-design-vue/es/calendar/index.js
@@ -3,7 +3,7 @@ import _slicedToArray from 'babel-runtime/helpers/slicedToArray';
 import PropTypes from '../_util/vue-types';
 import BaseMixin from '../_util/BaseMixin';
 import { getOptionProps, hasProp, initDefaultProps, getListeners } from '../_util/props-util';
-import * as moment from 'moment';
+import moment from 'moment';
 import FullCalendar from '../vc-calendar/src/FullCalendar';
 import Header from './Header';
 import LocaleReceiver from '../locale-provider/LocaleReceiver';
diff --git a/node_modules/ant-design-vue/es/locale-provider/index.js b/node_modules/ant-design-vue/es/locale-provider/index.js
index 657c8b6..284b877 100644
--- a/node_modules/ant-design-vue/es/locale-provider/index.js
+++ b/node_modules/ant-design-vue/es/locale-provider/index.js
@@ -1,6 +1,6 @@
 import _extends from 'babel-runtime/helpers/extends';
 import PropTypes from '../_util/vue-types';
-import * as moment from 'moment';
+import moment from 'moment';
 import interopDefault from '../_util/interopDefault';
 import { changeConfirmLocale } from '../modal/locale';
 import Base from '../base';
