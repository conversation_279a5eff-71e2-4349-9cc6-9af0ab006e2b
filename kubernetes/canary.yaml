apiVersion: flagger.app/v1beta1
kind: Canary
metadata:
  name: model-training-platform-service
  namespace: kezhaozhao-test
spec:
  # deployment reference
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: model-training-platform-service
  # the maximum time in seconds for the canary deployment
  # to make progress before it is rollback (default 600s)
  progressDeadlineSeconds: 300
  service:
    # container port
    port: 7001
    # Istio gateways (optional)
    gateways:
      #      - public-gateway.istio-system.svc.cluster.local
      - kezhaozhao-gateway.kezhaozhao
      - mesh
    hosts:
      - 't.test.greatld.com'
      - 'api.test.greatld.com'
      # - "*************" # IDC kubernetes cluster master node IP
    match:
      - uri:
          prefix: /
    timeout: 65s
    # Istio retry policy (optional)
    retries:
      attempts: 2
      perTryTimeout: 31s
      retryOn: 'gateway-error,connect-failure,refused-stream'
    headers:
      response:
        add:
          cache-control: 'private'
  skipAnalysis: true
  analysis:
    interval: 1m
    # max number of failed metric checks before rollback
    threshold: 3
    stepWeight: 10
    maxWeight: 30
    metrics:
      - name: request-success-rate
        thresholdRange:
          min: 99
        interval: 1m
    webhooks:
      - name: load-test
        url: http://flagger-loadtester.kezhaozhao-test/
        timeout: 5s
        metadata:
          cmd: 'hey -z 1m -q 10 -c 2 http://model-training-platform-service-canary.kezhaozhao-test:7001/api/health'
