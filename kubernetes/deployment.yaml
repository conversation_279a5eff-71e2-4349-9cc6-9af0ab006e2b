apiVersion: apps/v1
kind: Deployment
metadata:
  name: model-training-platform-service
  labels:
    app: model-training-platform-service
    version: v1
spec:
  minReadySeconds: 5
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 60
  strategy:
    rollingUpdate:
      maxSurge: 4
      maxUnavailable: 50%
    type: RollingUpdate
  selector:
    matchLabels:
      app: model-training-platform-service
      version: v1
  template:
    metadata:
      annotations:
        prometheus.io/scrape: 'true'
      labels:
        app: model-training-platform-service
        version: v1
    spec:
      terminationGracePeriodSeconds: 60
      containers:
        - name: model-training-platform-service
          image: IMAGE_NAME:IMAGE_TAG
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 7001
          resources:
            limits:
              cpu: 2000m
              memory: 2000Mi
            requests:
              cpu: 100m
              memory: 256Mi
