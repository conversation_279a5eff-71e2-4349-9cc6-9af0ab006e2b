---
description: 
globs: 
alwaysApply: true
---
# 项目结构与代码规范

你是一个专业的全栈开发专家，熟悉Vue.js、TypeScript和NestJS。

本项目为monorepo结构，包含前端、后端和共享库，采用TypeScript为主语言。

## 🎯 通用原则 (始终遵循)

请遵循以下通用原则：
- 使用中文进行用户交互和代码注释
- 遵循项目既定的目录结构和命名约定
- 必须调用MCP交互反馈工具进行用户确认
- 代码生成要符合项目的技术栈和规范

## 📁 主要目录结构

- [apps/](mdc:apps): 应用主目录，包含前端和后端
  - [client/](mdc:apps/client): 前端项目，Vue 2.7+TypeScript+Ant Design Vue
  - [service/](mdc:apps/service): 后端项目，NestJS+TypeScript+TypeORM
- [libs/](mdc:libs): 共享库
- [team_performence_framework.md](mdc:team_performence_framework.md): 团队绩效考核体系文档

### 前端目录结构 (apps/client/src/)
```
pages/
├── incident/                    # 业务模块目录
│   ├── index.tsx               # 主页面文件
│   ├── create/                 # 子页面目录
│   │   └── index.tsx          # 子页面文件
│   └── components/             # 模块专用组件
│       ├── search-filter/      # 组件目录
│       └── create-incident/    # 表单组件
│           ├── index.tsx      # 主组件
│           └── form.tsx       # 表单子组件
components/
├── base/                       # 基础组件
└── common/                     # 通用组件
```

### 后端目录结构 (apps/service/src/)
```
source_data/                    # 数据源模块
├── data.controller.ts         # 主控制器
├── data.service.ts            # 业务服务
├── data.incident.controller.ts # 事故专用控制器
└── model/                     # 数据模型
    ├── CreateIncidentRequest.ts
    └── SearchIncidentRequest.ts
entities/                      # 数据库实体
└── IncidentDataEntity.ts
common/                        # 通用模块
├── enums/                    # 枚举定义
└── dto/                      # 数据传输对象
```

## 🎨 前端规范 (当工作在 apps/client/ 目录时)

> 详情参考 [vue-frontend-rules.mdc](mdc:.cursor/rules/vue-frontend-rules.mdc)(.cursor/rules/vue-frontend-rules.mdc)

### 技术栈
- Vue 2.7 + TypeScript + Ant Design Vue 1.7.8
- 路由: Vue Router 3.x, 状态: Pinia, 构建: Vite, 样式: UnoCSS

### 组件代码模板
```typescript
import { defineComponent, ref } from 'vue';
import { Card } from '@/components/base';

const ComponentName = defineComponent({
  name: 'ComponentName',
  setup() {
    const loading = ref(false);
    return { loading };
  },
  methods: {
    handleSubmit() {
      const formComponent = this.$refs.formRef as any;
      formComponent.validateFieldsAndScroll((errors: any, values: any) => {
        // 处理逻辑
      });
    }
  },
  render() {
    return (
      <div class="p-6">
        <Card title="标题">
          <div class="flex flex-col gap-3">
            {/* 内容 */}
          </div>
        </Card>
      </div>
    );
  }
});
```

### 关键约定
- 组件名: PascalCase, 文件名: kebab-case
- 样式: UnoCSS原子类 (`p-6`, `flex`, `gap-3`)
- 路由: `this.$router.push('/path')`
- 错误处理: `Message.error()`
- 表单: `Form.create({})(Component)`

## 🔧 后端规范 (当工作在 apps/service/ 目录时)

> 详情参考 [nestjs-rules.mdc](mdc:.cursor/rules/nestjs-rules.mdc)(.cursor/rules/nestjs-rules.mdc)

### 技术栈
- NestJS + TypeScript + TypeORM
- 路径模式: `/api/data/{resource}`

### 控制器代码模板
```typescript
@Controller('api/data/incident')
export class DataIncidentController {
  constructor(private readonly dataService: DataService) {}

  @Post('create')
  @ApiOperation({ summary: '创建事故' })
  @ApiResponse({ status: 201, type: IncidentEntity })
  async create(@Body() request: CreateRequest) {
    return this.dataService.create(request);
  }
}
```

### 请求模型模板
```typescript
export class CreateRequest {
  @ApiProperty({ description: '字段描述' })
  @IsString()
  @IsNotEmpty()
  fieldName: string;

  @ApiProperty({ description: '可选字段', required: false })
  @IsOptional()
  optionalField?: string;
}
```

### 关键约定
- 类名: PascalCase, 文件名: kebab-case  
- 数据库列名: snake_case
- 验证: class-validator装饰器
- 错误: 使用NestJS标准异常
- 文档: 完整的Swagger注解

## 📋 详细规范参考

根据当前编辑的文件路径，自动应用对应的详细规范：
- **前端详细规范**: [vue-frontend-rules.mdc](mdc:.cursor/rules/vue-frontend-rules.mdc)
- **后端详细规范**: [nestjs-rules.mdc](mdc:.cursor/rules/nestjs-rules.mdc)
- **交互反馈规范**: [feedbackrule.mdc](mdc:.cursor/rules/feedbackrule.mdc)

## 🛠 工具配置
- 代码格式化：Prettier
- 代码检查：ESLint
- 前端测试：Vitest
- 后端测试：Jest
- 只读文档：README.md

## ⚠️ 注意事项
- node_modules、dist、build、output等目录已忽略
- 绩效考核文档只读
- 生成代码时根据文件路径自动应用相应的前端或后端规范
