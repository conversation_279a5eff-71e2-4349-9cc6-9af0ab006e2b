---
description: 
globs: apps/client/**/*
alwaysApply: false
---
# Vue 前端代码规范

基于 Vue 2.7 + TypeScript + Ant Design Vue 1.7.8 的前端代码规范。

## 技术栈
- **框架**: Vue 2.7 + TypeScript
- **UI库**: Ant Design Vue 1.7.8
- **路由**: Vue Router 3.x
- **状态管理**: Pinia
- **构建工具**: Vite
- **样式**: UnoCSS + Less

## 目录结构规范

```
pages/
├── incident/                    # 业务模块目录
│   ├── index.tsx               # 主页面文件
│   ├── create/                 # 子页面目录
│   │   └── index.tsx          # 子页面文件
│   └── components/             # 模块专用组件
│       ├── search-filter/      # 组件目录
│       └── create-incident/    # 表单组件
│           ├── index.tsx      # 主组件
│           └── form.tsx       # 表单子组件
```

## 代码风格规范

### 1. 组件定义模板
```typescript
import { defineComponent, onMounted, ref } from 'vue';
import { message as Message } from 'ant-design-vue';
import { Card } from '@/components/base';
import { useAppStore } from '@/store/app.store';

const ComponentName = defineComponent({
  name: 'ComponentName',
  setup() {
    const appStore = useAppStore();
    const loading = ref(false);
    const formRef = ref();
    
    return {
      loading,
      formRef,
    };
  },
  methods: {
    handleSubmit() {
      const formComponent = this.$refs.formRef as any;
      formComponent.validateFieldsAndScroll((errors: any, values: any) => {
        if (errors) {
          Message.error('请检查表单内容后重新提交');
          return;
        }
        // 处理提交逻辑
      });
    },
  },
  render() {
    return (
      <div class="p-6">
        <Card title="页面标题">
          <div class="flex flex-col gap-3">
            {/* 页面内容 */}
          </div>
        </Card>
      </div>
    );
  },
});

export default ComponentName;
```

### 2. 表单组件模板
```typescript
import { Button, Form, Input, Select } from 'ant-design-vue';
import { defineComponent, type PropType } from 'vue';
import { createFormRules } from '@/components/base/base-form';

const FormComponent = defineComponent({
  name: 'FormComponent',
  props: {
    form: {
      type: Object as PropType<any>,
      required: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['submit', 'cancel'],
  render() {
    return (
      <Form layout="vertical" form={this.form} onSubmit={this.$emit('submit')}>
        {/* 表单字段 */}
        <Form.Item>
          <div class="inline-flex items-center gap-3">
            <Button type="primary" htmlType="submit" loading={this.loading}>
              提交
            </Button>
            <Button onClick={() => this.$emit('cancel')}>
              取消
            </Button>
          </div>
        </Form.Item>
      </Form>
    );
  },
});

export default Form.create({})(FormComponent);
```

### 3. 关键规范
- **组件命名**: PascalCase (IncidentCreatePage)
- **文件命名**: kebab-case (index.tsx)
- **导入路径**: 使用 `@/` 别名
- **路由跳转**: 使用 `this.$router.push('/path')`
- **表单验证**: 使用 `v-decorator` 和 `validateFieldsAndScroll`
- **样式类名**: UnoCSS 原子类 (`p-6`, `flex`, `gap-3`)
- **错误处理**: 使用 `Message.error()` 显示错误
- **类型定义**: ref变量指定类型，props使用PropType

### 4. API调用模式
```typescript
try {
  this.loading = true;
  await kpiService.incident.createWithStatus(data);
  Message.success('操作成功');
  this.$router.push('/incident');
} catch (error) {
  Message.error('操作失败，请重试');
} finally {
  this.loading = false;
}
```

### 5. 常见模式
- **页面布局**: `<div class="p-6"><Card><div class="flex flex-col gap-3">`
- **表单布局**: `<Form layout="vertical">`
- **按钮组**: `<div class="inline-flex items-center gap-3">`
- **加载状态**: `loading={this.loading}`
