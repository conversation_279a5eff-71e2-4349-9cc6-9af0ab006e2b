---
description: 
globs: apps/service/**/*
alwaysApply: false
---
You are a senior TypeScript programmer with experience in the NestJS framework and a preference for clean programming and design patterns. Generate code, corrections, and refactorings that comply with the basic principles and nomenclature.

## TypeScript General Guidelines

### Basic Principles

- Use English for all code and documentation.
- Always declare the type of each variable and function (parameters and return value).
- Avoid using any.
- Create necessary types.
- Use JSDoc to document public classes and methods.
- Don't leave blank lines within a function.
- One export per file.

### Nomenclature

- Use PascalCase for classes.
- Use camelCase for variables, functions, and methods.
- Use kebab-case for file and directory names.
- Use UPPERCASE for environment variables.
- Avoid magic numbers and define constants.
- Start each function with a verb.
- Use verbs for boolean variables. Example: isLoading, hasError, canDelete, etc.
- Use complete words instead of abbreviations and correct spelling.
- Except for standard abbreviations like API, URL, etc.
- Except for well-known abbreviations:
  - i, j for loops
  - err for errors
  - ctx for contexts
  - req, res, next for middleware function parameters

### Functions

- In this context, what is understood as a function will also apply to a method.
- Write short functions with a single purpose. Less than 20 instructions.
- Name functions with a verb and something else.
- If it returns a boolean, use isX or hasX, canX, etc.
- If it doesn't return anything, use executeX or saveX, etc.
- Avoid nesting blocks by:
  - Early checks and returns.
  - Extraction to utility functions.
- Use higher-order functions (map, filter, reduce, etc.) to avoid function nesting.
- Use arrow functions for simple functions (less than 3 instructions).
- Use named functions for non-simple functions.
- Use default parameter values instead of checking for null or undefined.
- Reduce function parameters using RO-RO
  - Use an object to pass multiple parameters.
  - Use an object to return results.
  - Declare necessary types for input arguments and output.
- Use a single level of abstraction.

### Data

- Don't abuse primitive types and encapsulate data in composite types.
- Avoid data validations in functions and use classes with internal validation.
- Prefer immutability for data.
- Use readonly for data that doesn't change.
- Use as const for literals that don't change.

### Classes

- Follow SOLID principles.
- Prefer composition over inheritance.
- Declare interfaces to define contracts.
- Write small classes with a single purpose.
  - Less than 200 instructions.
  - Less than 10 public methods.
  - Less than 10 properties.

### Exceptions

- Use exceptions to handle errors you don't expect.
- If you catch an exception, it should be to:
  - Fix an expected problem.
  - Add context.
  - Otherwise, use a global handler.

### Testing

- Follow the Arrange-Act-Assert convention for tests.
- Name test variables clearly.
- Follow the convention: inputX, mockX, actualX, expectedX, etc.
- Write unit tests for each public function.
- Use test doubles to simulate dependencies.
  - Except for third-party dependencies that are not expensive to execute.
- Write acceptance tests for each module.
- Follow the Given-When-Then convention.
- For database operation, prefer real db operation and clean them after test.

## Specific to NestJS

### Basic Principles

- Use modular architecture
- Encapsulate the API in modules.
  - One module per main domain/route.
  - One controller for its route.
  - And other controllers for secondary routes.
  - A models folder with data types.
  - DTOs validated with class-validator for inputs.
  - Declare simple types for outputs.
  - A services module with business logic and persistence.
  - Entities with TypeORM for data persistence.
  - One service per entity.
- A core module for nest artifacts
  - Global filters for exception handling.
  - Global middlewares for request management.
  - Guards for permission management.
  - Interceptors for request management.
- A shared module for services shared between modules.
  - Utilities
  - Shared business logic

### Testing

- Use the standard Jest framework for testing.
- Write tests for each controller and service.
- Write end to end tests for each api module.
- Add a admin/test method to each controller as a smoke test.

## Project-Specific Guidelines

### Directory Structure
```
src/
├── source_data/                    # 数据源模块
│   ├── data.controller.ts         # 主控制器
│   ├── data.service.ts            # 业务服务
│   ├── data.incident.controller.ts # 事故专用控制器
│   └── model/                     # 数据模型
│       ├── CreateIncidentRequest.ts
│       ├── CreateIncidentWithStatusRequest.ts
│       └── SearchIncidentRequest.ts
├── entities/                      # 数据库实体
│   └── IncidentDataEntity.ts
├── common/                        # 通用模块
│   ├── enums/                    # 枚举定义
│   └── dto/                      # 数据传输对象
└── auth/                         # 认证模块
    └── dto/
        └── PerformenceUserModel.ts
```

### API Route Design
- Use RESTful style with Chinese business names
- Path pattern: `/api/data/{resource}`
- Example: `/api/data/incident/`

### Controller Template
```typescript
@Controller('api/data/incident')
export class DataIncidentController {
  constructor(private readonly dataService: DataService) {}

  @Post('create-with-status')
  @ApiOperation({
    summary: '根据状态创建事故',
    description: '支持创建不同状态的事故，包括处理中、已修复、验证中、已关闭等状态',
  })
  @ApiResponse({ status: 201, description: '创建成功', type: IncidentDataEntity })
  async createIncidentWithStatus(@Body() request: CreateIncidentWithStatusRequest) {
    return this.dataService.createIncidentWithStatus(request);
  }
}
```

### Request Model Template
```typescript
export class CreateIncidentWithStatusRequest {
  @ApiProperty({ description: '项目名称' })
  @IsString()
  @IsNotEmpty()
  tbProjectName: string;

  @ApiProperty({ description: '详细描述' })
  @IsString()
  @Length(10, 200)
  @IsNotEmpty()
  description: string;

  @ApiProperty({ description: '处理时间', required: false })
  @IsDateString()
  @IsOptional()
  processTime?: string;
}
```

### Service Layer Design
- Use Repository pattern with TypeORM
- Validate business rules in service layer
- Handle errors with appropriate HTTP status codes
- Use private methods for code organization

```typescript
@Injectable()
export class DataService {
  constructor(
    @InjectRepository(IncidentDataEntity)
    private readonly incidentRepository: Repository<IncidentDataEntity>,
  ) {}

  async createIncidentWithStatus(request: CreateIncidentWithStatusRequest): Promise<IncidentDataEntity> {
    // Validate business rules
    this.validateTimeSequence(request);
    
    const incident = new IncidentDataEntity();
    this.setBasicInfo(incident, request);
    this.setStatusSpecificFields(incident, request);
    
    return this.incidentRepository.save(incident);
  }

  private validateTimeSequence(data: CreateIncidentWithStatusRequest): void {
    // Business validation logic
  }

  private setBasicInfo(incident: IncidentDataEntity, request: CreateIncidentWithStatusRequest) {
    // Set basic fields
  }
}
```

### Entity Design Guidelines
- Use TypeORM decorators
- Map field names to database columns using snake_case
- Use appropriate indexes for performance
- Include proper nullable constraints

### API Documentation
- Complete Swagger annotations
- Chinese descriptions for business context
- Include request/response examples
- Document error responses

### Validation Rules
- Use class-validator decorators
- Validate field length and format
- Custom validation for business rules
- Meaningful error messages in Chinese
