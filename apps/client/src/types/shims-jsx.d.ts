/**
 * 解决 `domPropsInnerHTML` 类型错误
 */
declare module 'vue/types/jsx' {
  export interface HTMLAttributes {
    domPropsInnerHTML?: string | string[] | unknown;
  }

  //   // NOTE: 允许 td 标签有 width 属性
  //   export interface TdHTMLAttributes {
  //     width?: string;
  //   }
  //   export interface ThHTMLAttributes {
  //     width?: string;
  //   }

  //   /**
  //    * NOTE: 允许 svg 有额外标签 (FIXME: 删除该标签)
  //    */
  //   export interface SVGAttributes {
  //     'xmlns:v'?: string;
  //     'xmlns:xlink'?: string;
  //   }

  //   export interface AnchorHTMLAttributes {
  //     disabled?: boolean;
  //   }
}

export {};
