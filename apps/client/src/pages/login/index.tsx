import { defineComponent, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';

import { useUserStore } from '@/store/modules/user.store';

import './index.less';

export default defineComponent({
  name: 'LoginPage',
  setup() {
    const userStore = useUserStore();
    const loading = ref(false);

    const loginForm = reactive({
      username: '',
      password: '',
    });

    const handleSubmit = async (e: Event) => {
      e.preventDefault(); // 阻止默认表单提交行为

      if (!loginForm.username || !loginForm.password) {
        message.error('请输入用户名和密码');
        return;
      }

      loading.value = true;
      try {
        console.log('正在尝试登录:', loginForm.username);
        await userStore.login(loginForm.username, loginForm.password);
        message.success('登录成功');

        // 使用window.location进行重定向，确保在任何情况下都能工作
        const urlParams = new URLSearchParams(window.location.search);
        const redirectPath = urlParams.get('redirect') || '/';
        window.location.href = redirectPath;
      } catch (error: any) {
        console.error('登录错误:', error);
        message.error(error?.message || '登录失败，请检查用户名和密码');
      } finally {
        loading.value = false;
      }
    };

    // 添加一个点击事件处理函数，确保按钮点击也能触发登录
    const handleButtonClick = () => {
      handleSubmit(new Event('click'));
    };

    return () => (
      <div class="login-container">
        <div class="login-form-wrapper">
          <div class="login-header">
            <h1 class="login-title">模型训练平台</h1>
          </div>
          <a-form model={loginForm} class="login-form" onSubmit={handleSubmit}>
            <a-form-item name="username" rules={[{ required: true, message: '请输入用户名' }]}>
              <a-input
                value={loginForm.username}
                onChange={(e) => (loginForm.username = e.target.value)}
                size="large"
                placeholder="用户名"
              ></a-input>
            </a-form-item>
            <a-form-item name="password" rules={[{ required: true, message: '请输入密码' }]}>
              <a-input-password
                value={loginForm.password}
                onChange={(e) => (loginForm.password = e.target.value)}
                size="large"
                placeholder="密码"
              ></a-input-password>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" html-type="submit" size="large" loading={loading.value} block onClick={handleButtonClick}>
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    );
  },
});
