import { defineComponent, ref, onMounted, computed } from 'vue';
import { Card, Table, Button, Input, Select, Space, Tag, Tooltip, Modal, Row, Col, Statistic, Badge } from 'ant-design-vue';
// 使用 Ant Design Vue 1.7.8 内置图标

import { useDeploymentStore } from '@/store/modules/deployment.store';
import { useModelStore } from '@/store/modules/model.store';
const { Option } = Select;

export default defineComponent({
  name: 'DeploymentsPage',
  setup() {
    const deploymentStore = useDeploymentStore();
    const modelStore = useModelStore();
    const deployModalVisible = ref(false);
    const detailModalVisible = ref(false);
    const testModalVisible = ref(false);
    const selectedDeployment = ref(null);
    const searchText = ref('');
    const selectedStatus = ref('');
    const selectedModelType = ref('');

    // 表格列定义
    const columns = [
      {
        title: '部署名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        ellipsis: true,
      },
      {
        title: '模型',
        dataIndex: 'modelId',
        key: 'modelId',
        width: 150,
        ellipsis: true,
      },
      {
        title: '模型类型',
        dataIndex: 'modelType',
        key: 'modelType',
        width: 120,
      },
      {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
      },
      {
        title: '端点',
        dataIndex: 'endpoint',
        key: 'endpoint',
        width: 200,
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
      },
    ];

    // 状态选项
    const statusOptions = [
      { value: 'PENDING', label: '待部署' },
      { value: 'DEPLOYING', label: '部署中' },
      { value: 'RUNNING', label: '运行中' },
      { value: 'FAILED', label: '失败' },
      { value: 'STOPPED', label: '已停止' },
    ];

    // 模型类型选项
    const modelTypes = [
      { value: 'TECH_HEALTH', label: '科创健康性模型' },
      { value: 'INTERNAL_CONTROL_RISK', label: '内控风险模型' },
      { value: 'CREDIT_RISK', label: '信用风险模型' },
      { value: 'MARKET_RISK', label: '市场风险模型' },
    ];

    // 计算属性
    const filteredDeployments = computed(() => {
      let deployments = deploymentStore.deployments;

      if (searchText.value) {
        deployments = deployments.filter(
          (deployment) =>
            deployment.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
            deployment.description.toLowerCase().includes(searchText.value.toLowerCase())
        );
      }

      if (selectedStatus.value) {
        deployments = deployments.filter((deployment) => deployment.status === selectedStatus.value);
      }

      if (selectedModelType.value) {
        deployments = deployments.filter((deployment) => deployment.modelType === selectedModelType.value);
      }

      return deployments;
    });

    // 统计数据
    const statistics = computed(() => {
      const deployments = deploymentStore.deployments;
      return {
        total: deployments.length,
        running: deployments.filter((deployment) => deployment.status === 'RUNNING').length,
        failed: deployments.filter((deployment) => deployment.status === 'FAILED').length,
        pending: deployments.filter((deployment) => deployment.status === 'PENDING').length,
      };
    });

    // 方法
    const handleSearch = () => {
      fetchDeployments();
    };

    const handleReset = () => {
      searchText.value = '';
      selectedStatus.value = '';
      selectedModelType.value = '';
      fetchDeployments();
    };

    const fetchDeployments = async () => {
      try {
        await deploymentStore.fetchDeployments(deploymentStore.currentPage, deploymentStore.pageSize, {
          search: searchText.value,
          status: selectedStatus.value,
          modelType: selectedModelType.value,
        });
      } catch (error) {
        console.error('获取部署列表失败:', error);
      }
    };

    const showDeployModal = () => {
      deployModalVisible.value = true;
    };

    const showDetailModal = (deployment) => {
      selectedDeployment.value = deployment;
      detailModalVisible.value = true;
    };

    const showTestModal = (deployment) => {
      selectedDeployment.value = deployment;
      testModalVisible.value = true;
    };

    const handleRollback = (deployment) => {
      Modal.confirm({
        title: '确认回滚',
        content: `确定要回滚部署 "${deployment.name}" 到上一个版本吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            // 假设回滚到上一个版本，实际应该有版本选择
            await deploymentStore.rollbackDeployment(deployment.id, 'previous');
            await fetchDeployments();
          } catch (error) {
            console.error('回滚部署失败:', error);
          }
        },
      });
    };

    const handleDelete = (deployment) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除部署 "${deployment.name}" 吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await deploymentStore.deleteDeployment(deployment.id);
            await fetchDeployments();
          } catch (error) {
            console.error('删除部署失败:', error);
          }
        },
      });
    };

    const getStatusColor = (status) => {
      const statusMap = {
        PENDING: 'default',
        DEPLOYING: 'processing',
        RUNNING: 'success',
        FAILED: 'error',
        STOPPED: 'warning',
      };
      return statusMap[status] || 'default';
    };

    const getStatusText = (status) => {
      const statusMap = {
        PENDING: '待部署',
        DEPLOYING: '部署中',
        RUNNING: '运行中',
        FAILED: '失败',
        STOPPED: '已停止',
      };
      return statusMap[status] || status;
    };

    // 生命周期
    onMounted(() => {
      fetchDeployments();
    });

    return () => (
      <div class="p-6">
        <div class="mb-6">
          <h2 style={{ margin: '0 0 8px 0', fontSize: '24px', fontWeight: 600 }}>
            <a-icon type="cloud-server" class="mr-2" />
            模型部署
          </h2>
          <p style={{ color: '#999', margin: 0 }}>管理机器学习模型部署，支持版本控制、回滚和在线测试</p>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} class="mb-4">
          <Col span={6}>
            <Card>
              <Statistic title="总部署数" value={statistics.value.total} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="运行中" value={statistics.value.running} valueStyle={{ color: '#52c41a' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="待部署" value={statistics.value.pending} valueStyle={{ color: '#faad14' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="失败" value={statistics.value.failed} valueStyle={{ color: '#f5222d' }} />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选区域 */}
        <Card class="mb-4">
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                value={searchText.value}
                onChange={(e) => (searchText.value = e.target.value)}
                placeholder="搜索部署名称或描述"
                prefix={<a-icon type="search" />}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                value={selectedStatus.value}
                onChange={(value) => (selectedStatus.value = value)}
                placeholder="选择状态"
                allowClear
                style={{ width: '100%' }}
              >
                {statusOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                value={selectedModelType.value}
                onChange={(value) => (selectedModelType.value = value)}
                placeholder="选择模型类型"
                allowClear
                style={{ width: '100%' }}
              >
                {modelTypes.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" onClick={handleSearch} icon={<a-icon type="search" />}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
            <Col span={4} class="text-right">
              <Button type="primary" onClick={showDeployModal} icon={<a-icon type="plus" />}>
                部署模型
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 部署列表 */}
        <Card>
          <Table
            dataSource={filteredDeployments.value}
            columns={columns}
            loading={deploymentStore.loading}
            pagination={{
              current: deploymentStore.currentPage,
              pageSize: deploymentStore.pageSize,
              total: deploymentStore.totalCount,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                deploymentStore.fetchDeployments(page, pageSize, {
                  search: searchText.value,
                  status: selectedStatus.value,
                  modelType: selectedModelType.value,
                });
              },
            }}
            scroll={{ x: 1200 }}
            scopedSlots={{
              status: (text) => (
                <Badge status={getStatusColor(text) === 'success' ? 'success' : 'processing'} text={getStatusText(text)} />
              ),
              endpoint: (text) => (
                <a href={text} target="_blank" rel="noopener noreferrer">
                  <a-icon type="api" class="mr-1" />
                  {text}
                </a>
              ),
              createdAt: (text) => new Date(text).toLocaleString(),
              action: (text, record) => (
                <Space>
                  <Tooltip title="查看详情">
                    <Button type="link" size="small" icon={<a-icon type="eye" />} onClick={() => showDetailModal(record)} />
                  </Tooltip>
                  <Tooltip title="测试部署">
                    <Button
                      type="link"
                      size="small"
                      icon={<a-icon type="experiment" />}
                      onClick={() => showTestModal(record)}
                      disabled={record.status !== 'RUNNING'}
                    />
                  </Tooltip>
                  <Tooltip title="回滚版本">
                    <Button
                      type="link"
                      size="small"
                      icon={<a-icon type="rollback" />}
                      onClick={() => handleRollback(record)}
                      disabled={record.status !== 'RUNNING'}
                    />
                  </Tooltip>
                  <Tooltip title="删除">
                    <Button
                      type="link"
                      size="small"
                      danger
                      icon={<a-icon type="delete" />}
                      onClick={() => handleDelete(record)}
                    />
                  </Tooltip>
                </Space>
              ),
            }}
          />
        </Card>
      </div>
    );
  },
});
