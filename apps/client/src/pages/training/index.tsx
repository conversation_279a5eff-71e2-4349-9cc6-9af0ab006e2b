import Vue from 'vue';
import { Card, Table, Button, Input, Select, Space, Tag, Tooltip, Modal, Row, Col, Progress, Statistic } from 'ant-design-vue';
// 使用 Ant Design Vue 1.7.8 内置图标

import { useTrainingStore } from '@/store/modules/training.store';
const { Option } = Select;

export default Vue.extend({
  name: 'TrainingPage',
  data() {
    return {
      createModalVisible: false,
      detailModalVisible: false,
      selectedTrainingJob: null,
      searchText: '',
      selectedStatus: '',
      selectedModelType: '',
      // 表格列定义
      columns: [
        {
          title: '任务名称',
          dataIndex: 'name',
          key: 'name',
          width: 200,
          ellipsis: true,
        },
        {
          title: '数据集',
          dataIndex: 'datasetId',
          key: 'datasetId',
          width: 150,
          ellipsis: true,
        },
        {
          title: '模型类型',
          dataIndex: 'modelType',
          key: 'modelType',
          width: 120,
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
        },
        {
          title: '进度',
          key: 'progress',
          width: 120,
        },
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          width: 150,
        },
        {
          title: '运行时间',
          key: 'duration',
          width: 120,
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          fixed: 'right',
        },
      ],
      // 状态选项
      statusOptions: [
        { value: 'PENDING', label: '待启动' },
        { value: 'RUNNING', label: '运行中' },
        { value: 'COMPLETED', label: '已完成' },
        { value: 'FAILED', label: '失败' },
        { value: 'STOPPED', label: '已停止' },
      ],
      // 模型类型选项
      modelTypes: [
        { value: 'TECH_HEALTH', label: '科创健康性模型' },
        { value: 'INTERNAL_CONTROL_RISK', label: '内控风险模型' },
        { value: 'CREDIT_RISK', label: '信用风险模型' },
        { value: 'MARKET_RISK', label: '市场风险模型' },
      ],
    };
  },
  computed: {
    trainingStore() {
      return useTrainingStore();
    },
    filteredTrainingJobs() {
      let jobs = this.trainingStore.trainingJobs;

      if (this.searchText) {
        jobs = jobs.filter(
          (job) =>
            job.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
            job.description.toLowerCase().includes(this.searchText.toLowerCase())
        );
      }

      if (this.selectedStatus) {
        jobs = jobs.filter((job) => job.status === this.selectedStatus);
      }

      if (this.selectedModelType) {
        jobs = jobs.filter((job) => job.modelType === this.selectedModelType);
      }

      return jobs;
    },
    statistics() {
      const jobs = this.trainingStore.trainingJobs;
      return {
        total: jobs.length,
        running: jobs.filter((job) => job.status === 'RUNNING').length,
        completed: jobs.filter((job) => job.status === 'COMPLETED').length,
        failed: jobs.filter((job) => job.status === 'FAILED').length,
      };
    },
  },
  methods: {
    handleSearch() {
      this.fetchTrainingJobs();
    },
    handleReset() {
      this.searchText = '';
      this.selectedStatus = '';
      this.selectedModelType = '';
      this.fetchTrainingJobs();
    },
    async fetchTrainingJobs() {
      try {
        await this.trainingStore.fetchTrainingJobs(this.trainingStore.currentPage, this.trainingStore.pageSize, {
          search: this.searchText,
          status: this.selectedStatus,
          modelType: this.selectedModelType,
        });
      } catch (error) {
        console.error('获取训练任务列表失败:', error);
      }
    },
    showCreateModal() {
      this.createModalVisible = true;
    },
    showDetailModal(job) {
      this.selectedTrainingJob = job;
      this.detailModalVisible = true;
    },
    async handleStart(job) {
      try {
        await this.trainingStore.startTrainingJob(job.id);
        await this.fetchTrainingJobs();
      } catch (error) {
        console.error('启动训练任务失败:', error);
      }
    },
    async handleStop(job) {
      Modal.confirm({
        title: '确认停止',
        content: `确定要停止训练任务 "${job.name}" 吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await this.trainingStore.stopTrainingJob(job.id);
            await this.fetchTrainingJobs();
          } catch (error) {
            console.error('停止训练任务失败:', error);
          }
        },
      });
    },
    getStatusColor(status) {
      const statusMap = {
        PENDING: 'default',
        RUNNING: 'processing',
        COMPLETED: 'success',
        FAILED: 'error',
        STOPPED: 'warning',
      };
      return statusMap[status] || 'default';
    },
    getStatusText(status) {
      const statusMap = {
        PENDING: '待启动',
        RUNNING: '运行中',
        COMPLETED: '已完成',
        FAILED: '失败',
        STOPPED: '已停止',
      };
      return statusMap[status] || status;
    },
    calculateDuration(startedAt, completedAt) {
      if (!startedAt) return '-';
      const start = new Date(startedAt);
      const end = completedAt ? new Date(completedAt) : new Date();
      const duration = Math.floor((end.getTime() - start.getTime()) / 1000);

      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      const seconds = duration % 60;

      if (hours > 0) {
        return `${hours}h ${minutes}m ${seconds}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds}s`;
      } else {
        return `${seconds}s`;
      }
    },
    getProgress(job) {
      if (job.status === 'COMPLETED') return 100;
      if (job.status === 'FAILED' || job.status === 'STOPPED') return 0;
      if (job.status === 'RUNNING') {
        // 模拟进度，实际应该从后端获取
        return Math.floor(Math.random() * 80) + 10;
      }
      return 0;
    },
  },
  mounted() {
    this.fetchTrainingJobs();
  },
  render() {
    return (
      <div class="p-6">
        <div class="mb-6">
          <h2 style={{ margin: '0 0 8px 0', fontSize: '24px', fontWeight: 600 }}>
            <a-icon type="experiment" class="mr-2" />
            训练任务
          </h2>
          <p style={{ color: '#999', margin: 0 }}>管理机器学习模型训练任务，监控训练进度和性能指标</p>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} class="mb-4">
          <Col span={6}>
            <Card>
              <Statistic title="总任务数" value={this.statistics.total} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="运行中" value={this.statistics.running} valueStyle={{ color: '#1890ff' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="已完成" value={this.statistics.completed} valueStyle={{ color: '#52c41a' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="失败" value={this.statistics.failed} valueStyle={{ color: '#f5222d' }} />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选区域 */}
        <Card class="mb-4">
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                value={this.searchText}
                onChange={(e) => (this.searchText = e.target.value)}
                placeholder="搜索任务名称或描述"
                prefix={<a-icon type="search" />}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                value={this.selectedStatus}
                onChange={(value) => (this.selectedStatus = value)}
                placeholder="选择状态"
                allowClear
                style={{ width: '100%' }}
              >
                {this.statusOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                value={this.selectedModelType}
                onChange={(value) => (this.selectedModelType = value)}
                placeholder="选择模型类型"
                allowClear
                style={{ width: '100%' }}
              >
                {this.modelTypes.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" onClick={this.handleSearch} icon={<a-icon type="search" />}>
                  搜索
                </Button>
                <Button onClick={this.handleReset}>重置</Button>
              </Space>
            </Col>
            <Col span={4} class="text-right">
              <Button type="primary" onClick={this.showCreateModal} icon={<a-icon type="plus" />}>
                创建任务
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 训练任务列表 */}
        <Card>
          <Table
            dataSource={this.filteredTrainingJobs}
            columns={this.columns}
            loading={this.trainingStore.loading}
            pagination={{
              current: this.trainingStore.currentPage,
              pageSize: this.trainingStore.pageSize,
              total: this.trainingStore.totalCount,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                this.trainingStore.fetchTrainingJobs(page, pageSize, {
                  search: this.searchText,
                  status: this.selectedStatus,
                  modelType: this.selectedModelType,
                });
              },
            }}
            scroll={{ x: 1200 }}
            scopedSlots={{
              status: (text) => <Tag color={this.getStatusColor(text)}>{this.getStatusText(text)}</Tag>,
              progress: (text, record) => (
                <Progress
                  percent={this.getProgress(record)}
                  size="small"
                  status={record.status === 'FAILED' ? 'exception' : 'normal'}
                />
              ),
              createdAt: (text) => new Date(text).toLocaleString(),
              duration: (text, record) => this.calculateDuration(record.startedAt, record.completedAt),
              action: (text, record) => (
                <Space>
                  <Tooltip title="查看详情">
                    <Button type="link" size="small" icon={<a-icon type="eye" />} onClick={() => this.showDetailModal(record)} />
                  </Tooltip>
                  {record.status === 'PENDING' && (
                    <Tooltip title="启动">
                      <Button
                        type="link"
                        size="small"
                        icon={<a-icon type="play-circle" />}
                        onClick={() => this.handleStart(record)}
                      />
                    </Tooltip>
                  )}
                  {record.status === 'RUNNING' && (
                    <Tooltip title="停止">
                      <Button
                        type="link"
                        size="small"
                        danger
                        icon={<a-icon type="stop" />}
                        onClick={() => this.handleStop(record)}
                      />
                    </Tooltip>
                  )}
                  <Tooltip title="查看指标">
                    <Button type="link" size="small" icon={<a-icon type="line-chart" />} disabled={record.status === 'PENDING'} />
                  </Tooltip>
                </Space>
              ),
            }}
          />
        </Card>
      </div>
    );
  },
});
