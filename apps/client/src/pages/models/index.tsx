import Vue from 'vue';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  Modal,
  Row,
  Col,
  Statistic,
  Typography,
  Icon,
} from 'ant-design-vue';
// 使用 Ant Design Vue 1.7.8 内置图标

import { useModelStore } from '@/store/modules/model.store';
import { ModelTypeEnum } from '@/store/modules/dataset.store';
const { Option } = Select;
const { Title, Text } = Typography;

export default Vue.extend({
  name: 'ModelsPage',
  components: {
    Card,
    Table,
    Button,
    Input,
    Select,
    Option,
    Space,
    Tag,
    Tooltip,
    Modal,
    Row,
    Col,
    Statistic,
    Typography,
    Title,
    Text,
    Icon,
  },
  data() {
    return {
      detailModalVisible: false,
      compareModalVisible: false,
      previewModalVisible: false,
      selectedModel: null,
      selectedModels: [],
      searchText: '',
      selectedStatus: '',
      selectedModelType: ModelTypeEnum.INNOVATION_HEALTH_MODEL, // 默认选择科创健康性模型
      // 表格列定义
      columns: [
        {
          title: '模型名称',
          dataIndex: 'name',
          key: 'name',
          width: 200,
          ellipsis: true,
        },
        {
          title: '版本',
          dataIndex: 'version',
          key: 'version',
          width: 100,
        },
        {
          title: '模型类型',
          dataIndex: 'modelType',
          key: 'modelType',
          width: 120,
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
        },
        {
          title: '准确率',
          key: 'accuracy',
          width: 100,
        },
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          width: 150,
        },
        {
          title: '创建者',
          dataIndex: 'createdBy',
          key: 'createdBy',
          width: 120,
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          fixed: 'right',
        },
      ],
      // 状态选项
      statusOptions: [
        { value: 'DRAFT', label: '草稿' },
        { value: 'PENDING_APPROVAL', label: '待审批' },
        { value: 'APPROVED', label: '已批准' },
        { value: 'REJECTED', label: '已拒绝' },
        { value: 'DEPLOYED', label: '已部署' },
      ],
      // 模型类型选项，与后端枚举保持一致
      modelTypes: [
        { value: ModelTypeEnum.INNOVATION_HEALTH_MODEL, label: '科创健康性模型' },
        { value: ModelTypeEnum.GENERIC_RISK_MODEL, label: '内控风险模型' },
      ],
    };
  },
  computed: {
    modelStore() {
      return useModelStore();
    },
    filteredModels() {
      let models = this.modelStore.models;

      if (this.searchText) {
        models = models.filter(
          (model) =>
            model.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
            model.description.toLowerCase().includes(this.searchText.toLowerCase())
        );
      }

      if (this.selectedStatus) {
        models = models.filter((model) => model.status === this.selectedStatus);
      }

      if (this.selectedModelType) {
        models = models.filter((model) => model.modelType === this.selectedModelType);
      }

      return models;
    },
    statistics() {
      const models = this.modelStore.models;
      return {
        total: models.length,
        approved: models.filter((model) => model.status === 'APPROVED').length,
        deployed: models.filter((model) => model.status === 'DEPLOYED').length,
        pending: models.filter((model) => model.status === 'PENDING_APPROVAL').length,
      };
    },
  },
  methods: {
    handleSearch() {
      this.fetchModels();
    },
    handleReset() {
      this.searchText = '';
      this.selectedStatus = '';
      this.selectedModelType = ModelTypeEnum.INNOVATION_HEALTH_MODEL; // 重置为默认值
      this.fetchModels();
    },
    async fetchModels() {
      try {
        await this.modelStore.fetchModels(this.modelStore.currentPage, this.modelStore.pageSize, {
          search: this.searchText,
          status: this.selectedStatus,
          modelType: this.selectedModelType,
        });
      } catch (error) {
        console.error('获取模型列表失败:', error);
      }
    },
    showDetailModal(model) {
      console.log('显示详情模态框:', model);
      this.$set(this, 'selectedModel', model);
      this.$set(this, 'detailModalVisible', true);
    },
    showPreviewModal(model) {
      console.log('显示预览模态框:', model);
      this.$set(this, 'selectedModel', model);
      this.$set(this, 'previewModalVisible', true);
    },
    showCompareModal() {
      if (this.selectedModels.length < 2) {
        Modal.warning({
          title: '提示',
          content: '请至少选择两个模型进行比较',
        });
        return;
      }
      this.compareModalVisible = true;
    },
    async handleApprove(model) {
      Modal.confirm({
        title: '确认批准',
        content: `确定要批准模型 "${model.name}" 吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await this.modelStore.approveModel(model.id, {});
            await this.fetchModels();
          } catch (error) {
            console.error('批准模型失败:', error);
          }
        },
      });
    },
    async handleDownload(model, format = 'pkl') {
      try {
        await this.modelStore.downloadModel(model.id, format);
      } catch (error) {
        console.error('下载模型失败:', error);
      }
    },
    handleDelete(model) {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除模型 "${model.name}" 吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await this.modelStore.deleteModel(model.id);
            await this.fetchModels();
          } catch (error) {
            console.error('删除模型失败:', error);
          }
        },
      });
    },
    getStatusColor(status) {
      const statusMap = {
        DRAFT: 'default',
        PENDING_APPROVAL: 'warning',
        APPROVED: 'success',
        REJECTED: 'error',
        DEPLOYED: 'processing',
      };
      return statusMap[status] || 'default';
    },
    getStatusText(status) {
      const statusMap = {
        DRAFT: '草稿',
        PENDING_APPROVAL: '待审批',
        APPROVED: '已批准',
        REJECTED: '已拒绝',
        DEPLOYED: '已部署',
      };
      return statusMap[status] || status;
    },
    getStatusIcon(status) {
      const iconMap = {
        DRAFT: <Icon type="clock-circle" />,
        PENDING_APPROVAL: <Icon type="exclamation-circle" />,
        APPROVED: <Icon type="check-circle" />,
        REJECTED: <Icon type="exclamation-circle" />,
        DEPLOYED: <Icon type="check-circle" />,
      };
      return iconMap[status] || null;
    },
    formatAccuracy(metrics) {
      if (!metrics || !metrics.accuracy) return '-';
      return `${(metrics.accuracy * 100).toFixed(2)}%`;
    },
    getModelTypeText(modelType) {
      const typeMap = {
        [ModelTypeEnum.INNOVATION_HEALTH_MODEL]: '科创健康性模型',
        [ModelTypeEnum.GENERIC_RISK_MODEL]: '内控风险模型',
      };
      return typeMap[modelType] || modelType;
    },
  },
  mounted() {
    // 确保默认值被正确设置
    if (!this.selectedModelType) {
      this.selectedModelType = ModelTypeEnum.INNOVATION_HEALTH_MODEL;
    }
    this.fetchModels();
  },
  render() {
    return (
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <Icon type="appstore" style={{ marginRight: '8px' }} />
            模型管理
          </Title>
          <Text type="secondary">管理机器学习模型，支持版本控制、性能比较和审批流程</Text>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col span={6}>
            <Card>
              <Statistic title="总模型数" value={this.statistics.total} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="已批准" value={this.statistics.approved} valueStyle={{ color: '#52c41a' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="已部署" value={this.statistics.deployed} valueStyle={{ color: '#1890ff' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="待审批" value={this.statistics.pending} valueStyle={{ color: '#faad14' }} />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选区域 */}
        <Card style={{ marginBottom: '16px' }}>
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                value={this.searchText}
                onChange={(e) => (this.searchText = e.target.value)}
                placeholder="搜索模型名称或描述"
                prefix={<Icon type="search" />}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                value={this.selectedStatus}
                onChange={(value) => (this.selectedStatus = value)}
                placeholder="选择状态"
                allowClear
                style={{ width: '100%' }}
              >
                {this.statusOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                value={this.selectedModelType}
                onChange={(value) => (this.selectedModelType = value)}
                placeholder="选择模型类型"
                style={{ width: '100%' }}
              >
                {this.modelTypes.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" onClick={this.handleSearch} icon={<Icon type="search" />}>
                  搜索
                </Button>
                <Button onClick={this.handleReset}>重置</Button>
                <Button
                  onClick={this.showCompareModal}
                  icon={<Icon type="bar-chart" />}
                  disabled={this.selectedModels.length < 2}
                >
                  比较模型
                </Button>
              </Space>
            </Col>
            <Col span={4} style={{ textAlign: 'right' }}>
              <Button type="primary" icon={<Icon type="plus" />}>
                注册模型
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 模型列表 */}
        <Card>
          <Table
            dataSource={this.filteredModels}
            columns={this.columns}
            loading={this.modelStore.loading}
            rowSelection={{
              selectedRowKeys: this.selectedModels,
              onChange: (selectedRowKeys) => {
                this.selectedModels = selectedRowKeys;
              },
            }}
            pagination={{
              current: this.modelStore.currentPage,
              pageSize: this.modelStore.pageSize,
              total: this.modelStore.totalCount,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                this.modelStore.fetchModels(page, pageSize, {
                  search: this.searchText,
                  status: this.selectedStatus,
                  modelType: this.selectedModelType,
                });
              },
            }}
            scroll={{ x: 1200 }}
            scopedSlots={{
              status: (text) => (
                <Tag color={this.getStatusColor(text)} icon={this.getStatusIcon(text)}>
                  {this.getStatusText(text)}
                </Tag>
              ),
              accuracy: (text, record) => this.formatAccuracy(record.metrics),
              createdAt: (text) => new Date(text).toLocaleString(),
              action: (text, record) => (
                <Space>
                  <Tooltip title="查看详情">
                    <Button type="link" size="small" icon={<Icon type="eye" />} onClick={() => this.showDetailModal(record)} />
                  </Tooltip>
                  <Tooltip title="预览">
                    <Button
                      type="link"
                      size="small"
                      icon={<Icon type="file-search" />}
                      onClick={() => this.showPreviewModal(record)}
                    />
                  </Tooltip>
                  {record.status === 'PENDING_APPROVAL' && (
                    <Tooltip title="批准">
                      <Button
                        type="link"
                        size="small"
                        icon={<Icon type="check-circle" />}
                        onClick={() => this.handleApprove(record)}
                      />
                    </Tooltip>
                  )}
                  <Tooltip title="下载">
                    <Button
                      type="link"
                      size="small"
                      icon={<Icon type="download" />}
                      onClick={() => this.handleDownload(record)}
                    />
                  </Tooltip>
                  <Tooltip title="删除">
                    <Button
                      type="link"
                      size="small"
                      danger
                      icon={<Icon type="delete" />}
                      onClick={() => this.handleDelete(record)}
                    />
                  </Tooltip>
                </Space>
              ),
            }}
          />
        </Card>

        {/* 模型详情模态框 */}
        <Modal
          title="模型详情"
          visible={this.detailModalVisible}
          onCancel={() => this.$set(this, 'detailModalVisible', false)}
          footer={null}
          width={800}
        >
          {this.selectedModel ? (
            <div>
              <Row gutter={16}>
                <Col span={12}>
                  <div style={{ marginBottom: '16px' }}>
                    <span style={{ fontWeight: 600 }}>模型名称：</span>
                    <span>{this.selectedModel.name}</span>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <span style={{ fontWeight: 600 }}>版本：</span>
                    <span>{this.selectedModel.version}</span>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <span style={{ fontWeight: 600 }}>模型类型：</span>
                    <span>{this.getModelTypeText(this.selectedModel.modelType)}</span>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <span style={{ fontWeight: 600 }}>状态：</span>
                    <Tag color={this.getStatusColor(this.selectedModel.status)}>
                      {this.getStatusText(this.selectedModel.status)}
                    </Tag>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ marginBottom: '16px' }}>
                    <span style={{ fontWeight: 600 }}>准确率：</span>
                    <span>{this.formatAccuracy(this.selectedModel.metrics)}</span>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <span style={{ fontWeight: 600 }}>创建时间：</span>
                    <span>{new Date(this.selectedModel.createdAt).toLocaleString()}</span>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <span style={{ fontWeight: 600 }}>创建者：</span>
                    <span>{this.selectedModel.createdBy}</span>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <span style={{ fontWeight: 600 }}>描述：</span>
                    <span>{this.selectedModel.description || '暂无描述'}</span>
                  </div>
                </Col>
              </Row>
              {this.selectedModel.parameters && (
                <div style={{ marginTop: '16px' }}>
                  <h4 style={{ margin: '0 0 16px 0', fontSize: '16px', fontWeight: 600 }}>训练参数</h4>
                  <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px', fontSize: '12px' }}>
                    {JSON.stringify(this.selectedModel.parameters, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <p>加载中...</p>
            </div>
          )}
        </Modal>

        {/* 模型预览模态框 */}
        <Modal
          title={`模型预览 - ${this.selectedModel ? this.selectedModel.name : ''}`}
          visible={this.previewModalVisible}
          onCancel={() => this.$set(this, 'previewModalVisible', false)}
          footer={null}
          width="90%"
          style={{ top: '20px' }}
        >
          {this.selectedModel ? (
            <div>
              <Row gutter={16} style={{ marginBottom: '16px' }}>
                <Col span={8}>
                  <Card title="基本信息" size="small">
                    <p>
                      <strong>模型名称：</strong>
                      {this.selectedModel.name}
                    </p>
                    <p>
                      <strong>版本：</strong>
                      {this.selectedModel.version}
                    </p>
                    <p>
                      <strong>类型：</strong>
                      {this.getModelTypeText(this.selectedModel.modelType)}
                    </p>
                    <p>
                      <strong>状态：</strong>
                      <Tag color={this.getStatusColor(this.selectedModel.status)}>
                        {this.getStatusText(this.selectedModel.status)}
                      </Tag>
                    </p>
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="性能指标" size="small">
                    <p>
                      <strong>准确率：</strong>
                      {this.formatAccuracy(this.selectedModel.metrics)}
                    </p>
                    {this.selectedModel.metrics &&
                      Object.entries(this.selectedModel.metrics).map(
                        ([key, value]) =>
                          key !== 'accuracy' && (
                            <p key={key}>
                              <strong>{key}：</strong>
                              {typeof value === 'number' ? value.toFixed(4) : value}
                            </p>
                          )
                      )}
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="训练信息" size="small">
                    <p>
                      <strong>创建时间：</strong>
                      {new Date(this.selectedModel.createdAt).toLocaleString()}
                    </p>
                    <p>
                      <strong>创建者：</strong>
                      {this.selectedModel.createdBy}
                    </p>
                    <p>
                      <strong>训练任务ID：</strong>
                      {this.selectedModel.trainingJobId || '无'}
                    </p>
                    <p>
                      <strong>数据集ID：</strong>
                      {this.selectedModel.datasetId || '无'}
                    </p>
                  </Card>
                </Col>
              </Row>

              {this.selectedModel.parameters && (
                <Card title="训练参数详情" size="small">
                  <pre
                    style={{
                      background: '#f5f5f5',
                      padding: '16px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      maxHeight: '300px',
                      overflow: 'auto',
                    }}
                  >
                    {JSON.stringify(this.selectedModel.parameters, null, 2)}
                  </pre>
                </Card>
              )}
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <p>加载中...</p>
            </div>
          )}
        </Modal>
      </div>
    );
  },
});
