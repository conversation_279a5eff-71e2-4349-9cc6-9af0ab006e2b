import Vue from 'vue';
import { Modal, Upload, Form, Input, Select, Button, message, Steps, Card, Row, Col, Progress } from 'ant-design-vue';
// 使用 Ant Design Vue 1.7.8 内置图标

import { useDatasetStore, ModelTypeEnum } from '@/store/modules/dataset.store';
const { Option } = Select;
const { Step } = Steps;
const { Dragger } = Upload;

export default Vue.extend({
  name: 'DatasetUpload',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentStep: 0,
      uploading: false,
      uploadProgress: 0,
      createdDatasetId: '',
      formData: {
        name: '',
        description: '',
        modelType: '',
        tags: [],
      },
      fileList: [],
      // 模型类型选项，与后端枚举保持一致
      modelTypes: [
        { value: ModelTypeEnum.INNOVATION_HEALTH_MODEL, label: '科创健康性模型' },
        { value: ModelTypeEnum.GENERIC_RISK_MODEL, label: '内控风险模型' },
      ],
    };
  },
  computed: {
    datasetStore() {
      return useDatasetStore();
    },
    uploadProps() {
      return {
        name: 'file',
        multiple: false,
        accept: '.csv,.xlsx,.xls',
        beforeUpload: (file) => {
          const isValidType =
            file.type === 'text/csv' ||
            file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.type === 'application/vnd.ms-excel';

          if (!isValidType) {
            message.error('只支持 CSV 和 Excel 文件格式');
            return false;
          }

          const isLt100M = file.size / 1024 / 1024 < 100;
          if (!isLt100M) {
            message.error('文件大小不能超过 100MB');
            return false;
          }

          this.fileList = [file];
          return false; // 阻止自动上传
        },
        onRemove: () => {
          this.fileList = [];
        },
      };
    },
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false);
      this.resetForm();
    },
    resetForm() {
      this.currentStep = 0;
      this.uploading = false;
      this.uploadProgress = 0;
      this.createdDatasetId = '';
      Object.assign(this.formData, {
        name: '',
        description: '',
        modelType: '',
        tags: [],
      });
      this.fileList = [];
    },
    handleNext() {
      if (this.currentStep === 0 && this.fileList.length === 0) {
        message.error('请先选择文件');
        return;
      }
      if (this.currentStep === 1) {
        if (!this.formData.name || !this.formData.modelType) {
          message.error('请填写必要信息');
          return;
        }
      }
      this.currentStep++;
    },
    handlePrev() {
      this.currentStep--;
    },
    // 第一步：创建数据集
    async handleCreateDataset() {
      if (!this.formData.name || !this.formData.modelType) {
        message.error('请填写必要信息');
        return;
      }

      this.uploading = true;
      this.uploadProgress = 10;

      try {
        const result = await this.datasetStore.createDataset({
          name: this.formData.name,
          description: this.formData.description,
          modelType: this.formData.modelType as ModelTypeEnum,
          tags: this.formData.tags,
        });

        this.createdDatasetId = result.id;
        this.uploadProgress = 30;
        this.currentStep = 2; // 跳到上传文件步骤

        message.success('数据集创建成功');
      } catch (error) {
        message.error('创建数据集失败：' + error.message);
      } finally {
        this.uploading = false;
      }
    },
    // 第二步：上传文件
    async handleUploadFile() {
      if (!this.createdDatasetId) {
        message.error('请先创建数据集');
        return;
      }

      if (this.fileList.length === 0) {
        message.error('请选择文件');
        return;
      }

      this.uploading = true;
      this.uploadProgress = 30;

      try {
        // 模拟上传进度
        const progressInterval = setInterval(() => {
          if (this.uploadProgress < 90) {
            this.uploadProgress += 10;
          }
        }, 200);

        await this.datasetStore.uploadDatasetFile(this.createdDatasetId, this.fileList[0].originFileObj || this.fileList[0]);

        clearInterval(progressInterval);
        this.uploadProgress = 100;

        setTimeout(() => {
          this.currentStep = 3; // 跳到完成步骤
          this.$emit('success');
        }, 500);
      } catch (error) {
        message.error('文件上传失败：' + error.message);
      } finally {
        this.uploading = false;
      }
    },
  },
  render() {
    return (
      <Modal title="上传数据集" visible={this.visible} onCancel={this.handleClose} footer={null} width={800} destroyOnClose>
        <Steps current={this.currentStep} class="mb-6">
          <Step title="选择文件" icon={<a-icon type="cloud-upload" />} />
          <Step title="填写信息" icon={<a-icon type="inbox" />} />
          <Step title="上传确认" icon={this.uploading ? <a-icon type="loading" /> : undefined} />
          <Step title="完成" icon={<a-icon type="check-circle" />} />
        </Steps>

        {/* 步骤1：选择文件 */}
        {this.currentStep === 0 && (
          <Card>
            <Dragger {...this.uploadProps} fileList={this.fileList}>
              <p class="ant-upload-drag-icon">
                <a-icon type="inbox" />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">支持 CSV、Excel 格式，文件大小不超过 100MB</p>
            </Dragger>
            <div class="mt-4 text-right">
              <Button type="primary" onClick={this.handleNext} disabled={this.fileList.length === 0}>
                下一步
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤2：填写信息 */}
        {this.currentStep === 1 && (
          <Card>
            <Form layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="数据集名称" required>
                    <Input
                      value={this.formData.name}
                      onChange={(e) => (this.formData.name = e.target.value)}
                      placeholder="请输入数据集名称"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="模型类型" required>
                    <Select
                      value={this.formData.modelType}
                      onChange={(value) => (this.formData.modelType = value)}
                      placeholder="请选择模型类型"
                    >
                      {this.modelTypes.map((type) => (
                        <Option key={type.value} value={type.value}>
                          {type.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item label="描述">
                <Input.TextArea
                  value={this.formData.description}
                  onChange={(e) => (this.formData.description = e.target.value)}
                  placeholder="请输入数据集描述"
                  rows={4}
                />
              </Form.Item>
            </Form>
            <div class="text-right">
              <Button onClick={this.handlePrev} class="mr-2">
                上一步
              </Button>
              <Button type="primary" onClick={this.handleCreateDataset} loading={this.uploading}>
                创建数据集
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤3：上传确认 */}
        {this.currentStep === 2 && (
          <Card>
            <div class="mb-4">
              <h4 style={{ margin: 0, fontSize: '16px', fontWeight: 600 }}>确认上传信息</h4>
            </div>
            <Row gutter={16}>
              <Col span={12}>
                <div class="mb-2">
                  <span style={{ fontWeight: 600 }}>文件名：</span>
                  <span>{this.fileList[0]?.name}</span>
                </div>
                <div class="mb-2">
                  <span style={{ fontWeight: 600 }}>数据集名称：</span>
                  <span>{this.formData.name}</span>
                </div>
                <div class="mb-2">
                  <span style={{ fontWeight: 600 }}>模型类型：</span>
                  <span>{this.modelTypes.find((t) => t.value === this.formData.modelType)?.label}</span>
                </div>
              </Col>
              <Col span={12}>
                <div class="mb-2">
                  <span style={{ fontWeight: 600 }}>文件大小：</span>
                  <span>{(this.fileList[0]?.size / 1024 / 1024).toFixed(2)} MB</span>
                </div>
                <div class="mb-2">
                  <span style={{ fontWeight: 600 }}>描述：</span>
                  <span>{this.formData.description || '无'}</span>
                </div>
              </Col>
            </Row>

            {this.uploading && (
              <div class="mt-4">
                <Progress percent={this.uploadProgress} status="active" />
                <span style={{ color: '#999' }}>正在上传...</span>
              </div>
            )}

            <div class="mt-4 text-right">
              <Button onClick={this.handlePrev} disabled={this.uploading} class="mr-2">
                上一步
              </Button>
              <Button type="primary" onClick={this.handleUploadFile} loading={this.uploading}>
                上传文件
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤4：完成 */}
        {this.currentStep === 3 && (
          <Card class="text-center">
            <a-icon type="check-circle" style={{ fontSize: '48px', color: '#52c41a' }} />
            <div class="mt-4">
              <h3 style={{ margin: '0 0 8px 0', fontSize: '20px', fontWeight: 600 }}>上传成功！</h3>
              <p style={{ color: '#999', margin: 0 }}>数据集已成功上传，系统正在处理中...</p>
            </div>
            <div class="mt-6">
              <Button type="primary" onClick={this.handleClose}>
                完成
              </Button>
            </div>
          </Card>
        )}
      </Modal>
    );
  },
});
