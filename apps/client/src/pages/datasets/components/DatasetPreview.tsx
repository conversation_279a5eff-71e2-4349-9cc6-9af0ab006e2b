import { defineComponent, ref, onMounted } from 'vue';
import { Modal, Table, Spin, Alert, Descriptions, Tabs, Card, Tag, Empty } from 'ant-design-vue';

import { useDatasetStore } from '@/store/modules/dataset.store';
const { TabPane } = Tabs;

export default defineComponent({
  name: 'DatasetPreview',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dataset: {
      type: Object,
      default: null,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const datasetStore = useDatasetStore();
    const previewData = ref([]);
    const previewColumns = ref([]);
    const loading = ref(false);
    const error = ref(null);
    const currentPage = ref(1);
    const pageSize = ref(50);
    const total = ref(0);

    const handleClose = () => {
      emit('update:visible', false);
    };

    const fetchPreviewData = async () => {
      if (!props.dataset?.id) return;

      loading.value = true;
      error.value = null;

      try {
        const response = await datasetStore.getDatasetPreview(props.dataset.id, currentPage.value, pageSize.value);

        // 更新分页信息和数据
        if (response.data) {
          total.value = response.totalCount || 0;

          // 构建表格列
          if (response.data.length > 0) {
            const firstRow = response.data[0];
            previewColumns.value = Object.keys(firstRow).map((key, index) => ({
              title: key,
              dataIndex: key,
              key: key,
              width: 150,
              ellipsis: true,
              fixed: index === 0 ? 'left' : undefined,
            }));

            // 添加行号
            previewData.value = response.data.map((row, index) => ({
              ...row,
              _rowIndex: (currentPage.value - 1) * pageSize.value + index + 1,
              key: (currentPage.value - 1) * pageSize.value + index,
            }));
          } else {
            previewColumns.value = [];
            previewData.value = [];
          }
        }
      } catch (err) {
        error.value = err.message || '获取预览数据失败';
      } finally {
        loading.value = false;
      }
    };

    // 分页处理函数
    const handlePageChange = (page, size) => {
      currentPage.value = page;
      pageSize.value = size;
      fetchPreviewData();
    };

    // 监听数据集变化
    onMounted(() => {
      if (props.visible && props.dataset) {
        fetchPreviewData();
      }
    });

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // 获取状态颜色
    const getStatusColor = (status) => {
      const statusMap = {
        ready: 'success',
        processing: 'processing',
        error: 'error',
        uploading: 'warning',
      };
      return statusMap[status] || 'default';
    };

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        ready: '就绪',
        processing: '处理中',
        error: '错误',
        uploading: '上传中',
      };
      return statusMap[status] || status;
    };

    return () => (
      <Modal
        title={`数据集预览 - ${props.dataset?.name || ''}`}
        visible={props.visible}
        onCancel={handleClose}
        footer={null}
        width="90%"
        style={{ top: '20px' }}
        destroyOnClose
      >
        {props.dataset && (
          <Tabs defaultActiveKey="info">
            <TabPane tab="基本信息" key="info">
              <Card>
                <Descriptions bordered column={2}>
                  <Descriptions.Item label="数据集名称">{props.dataset.name}</Descriptions.Item>
                  <Descriptions.Item label="模型类型">
                    <Tag color="blue">{props.dataset.modelType}</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={getStatusColor(props.dataset.status)}>{getStatusText(props.dataset.status)}</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="文件大小">{formatFileSize(props.dataset.fileSize)}</Descriptions.Item>
                  <Descriptions.Item label="行数">{props.dataset.rowCount?.toLocaleString() || 0}</Descriptions.Item>
                  <Descriptions.Item label="列数">{props.dataset.columnCount || 0}</Descriptions.Item>
                  <Descriptions.Item label="创建时间">{new Date(props.dataset.createdAt).toLocaleString()}</Descriptions.Item>
                  <Descriptions.Item label="创建者">{props.dataset.createdBy}</Descriptions.Item>
                  <Descriptions.Item label="描述" span={2}>
                    {props.dataset.description || '无描述'}
                  </Descriptions.Item>
                  {props.dataset.tags && props.dataset.tags.length > 0 && (
                    <Descriptions.Item label="标签" span={2}>
                      {props.dataset.tags.map((tag) => (
                        <Tag key={tag} color="geekblue">
                          {tag}
                        </Tag>
                      ))}
                    </Descriptions.Item>
                  )}
                </Descriptions>
              </Card>
            </TabPane>

            <TabPane tab="数据预览" key="preview">
              <Card>
                <Spin spinning={loading.value}>
                  {error.value ? (
                    <Alert message="加载失败" description={error.value} type="error" showIcon />
                  ) : previewData.value.length > 0 ? (
                    <div>
                      <div class="mb-4">
                        <span style={{ color: '#999' }}>
                          共 {total.value} 行数据，当前显示第 {(currentPage.value - 1) * pageSize.value + 1} -{' '}
                          {Math.min(currentPage.value * pageSize.value, total.value)} 行
                        </span>
                      </div>
                      <Table
                        dataSource={previewData.value}
                        columns={[
                          {
                            title: '#',
                            dataIndex: '_rowIndex',
                            key: '_rowIndex',
                            width: 60,
                            fixed: 'left',
                          },
                          ...previewColumns.value,
                        ]}
                        scroll={{ x: 1500, y: 400 }}
                        pagination={{
                          current: currentPage.value,
                          pageSize: pageSize.value,
                          total: total.value,
                          showSizeChanger: true,
                          showQuickJumper: true,
                          showTotal: (total, range) => `共 ${total} 条记录`,
                          onChange: handlePageChange,
                          onShowSizeChange: handlePageChange,
                        }}
                        size="small"
                        bordered
                      />
                    </div>
                  ) : (
                    <Empty description="暂无预览数据" />
                  )}
                </Spin>
              </Card>
            </TabPane>

            <TabPane tab="数据模式" key="schema">
              <Card>
                {props.dataset.schema ? (
                  <div>
                    <h4 style={{ margin: '0 0 16px 0', fontSize: '16px', fontWeight: 600 }}>字段信息</h4>
                    <Table
                      dataSource={Object.entries(props.dataset.schema).map(([key, value]) => ({
                        field: key,
                        type: value.type,
                        description: value.description,
                        required: value.required,
                        example: value.example,
                      }))}
                      columns={[
                        {
                          title: '字段名',
                          dataIndex: 'field',
                          key: 'field',
                          width: 150,
                        },
                        {
                          title: '类型',
                          dataIndex: 'type',
                          key: 'type',
                          width: 100,
                          render: (text) => <Tag color="purple">{text}</Tag>,
                        },
                        {
                          title: '必填',
                          dataIndex: 'required',
                          key: 'required',
                          width: 80,
                          render: (required) => <Tag color={required ? 'red' : 'default'}>{required ? '是' : '否'}</Tag>,
                        },
                        {
                          title: '描述',
                          dataIndex: 'description',
                          key: 'description',
                          ellipsis: true,
                        },
                        {
                          title: '示例',
                          dataIndex: 'example',
                          key: 'example',
                          width: 150,
                          ellipsis: true,
                        },
                      ]}
                      pagination={false}
                      size="small"
                    />
                  </div>
                ) : (
                  <Empty description="暂无模式信息" />
                )}
              </Card>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    );
  },
});
