import { defineComponent, ref, reactive, watch } from 'vue';
import { Modal, Form, Input, Select, Button, message } from 'ant-design-vue';

import { useDatasetStore, ModelTypeEnum } from '@/store/modules/dataset.store';

const { Option } = Select;

export default defineComponent({
  name: 'DatasetEdit',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dataset: {
      type: Object,
      default: null,
    },
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const datasetStore = useDatasetStore();
    const loading = ref(false);

    const formData = reactive({
      name: '',
      description: '',
      modelType: '',
      tags: [],
    });

    // 模型类型选项，与后端枚举保持一致
    const modelTypes = [
      { value: ModelTypeEnum.INNOVATION_HEALTH_MODEL, label: '科创健康性模型' },
      { value: ModelTypeEnum.GENERIC_RISK_MODEL, label: '内控风险模型' },
    ];

    // 监听数据集变化，更新表单数据
    watch(
      () => props.dataset,
      (newDataset) => {
        if (newDataset) {
          Object.assign(formData, {
            name: newDataset.name || '',
            description: newDataset.description || '',
            modelType: newDataset.modelType || '',
            tags: newDataset.tags || [],
          });
        }
      },
      { immediate: true }
    );

    const handleClose = () => {
      emit('update:visible', false);
      resetForm();
    };

    const resetForm = () => {
      Object.assign(formData, {
        name: '',
        description: '',
        modelType: '',
        tags: [],
      });
    };

    const handleSubmit = async () => {
      if (!formData.name || !formData.modelType) {
        message.error('请填写必要信息');
        return;
      }

      if (!props.dataset?.id) {
        message.error('数据集ID不存在');
        return;
      }

      loading.value = true;

      try {
        await datasetStore.updateDataset(props.dataset.id, {
          name: formData.name,
          description: formData.description,
          modelType: formData.modelType as ModelTypeEnum,
          tags: formData.tags,
        });

        message.success('数据集更新成功');
        emit('success');
        handleClose();
      } catch (error) {
        message.error('更新失败：' + error.message);
      } finally {
        loading.value = false;
      }
    };

    return () => (
      <Modal title="编辑数据集" visible={props.visible} onCancel={handleClose} footer={null} width={600} destroyOnClose>
        <Form layout="vertical">
          <Form.Item label="数据集名称" required>
            <Input v-model:value={formData.name} placeholder="请输入数据集名称" maxlength={100} />
          </Form.Item>

          <Form.Item label="模型类型" required>
            <Select v-model:value={formData.modelType} placeholder="请选择模型类型">
              {modelTypes.map((type) => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="描述">
            <Input.TextArea v-model:value={formData.description} placeholder="请输入数据集描述" rows={4} maxlength={500} />
          </Form.Item>

          <Form.Item label="标签">
            <Select v-model:value={formData.tags} mode="tags" placeholder="请输入标签，按回车添加" style={{ width: '100%' }} />
          </Form.Item>

          <div class="text-right">
            <Button onClick={handleClose} class="mr-2">
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit} loading={loading.value}>
              保存
            </Button>
          </div>
        </Form>
      </Modal>
    );
  },
});
