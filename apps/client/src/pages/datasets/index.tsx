import { defineComponent, ref, onMounted } from 'vue';
import { Card, Table, Button, Input, Select, Space, Tag, Tooltip, Modal, Row, Col } from 'ant-design-vue';

import { useDatasetStore, ModelTypeEnum, DatasetStatus } from '@/store/modules/dataset.store';

import DatasetUpload from './components/DatasetUpload';
import DatasetPreview from './components/DatasetPreview';
import DatasetEdit from './components/DatasetEdit';
const { Option } = Select;

export default defineComponent({
  name: 'DatasetsPage',
  setup() {
    const datasetStore = useDatasetStore();
    const uploadModalVisible = ref(false);
    const detailModalVisible = ref(false);
    const previewModalVisible = ref(false);
    const editModalVisible = ref(false);
    const selectedDataset = ref<any>(null);
    const searchText = ref('');
    const selectedModelType = ref('');

    // 表格列定义
    const columns = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        ellipsis: true,
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        width: 250,
        ellipsis: true,
      },
      {
        title: '模型类型',
        dataIndex: 'modelType',
        key: 'modelType',
        width: 150,
        customRender: (text) => <Tag color="blue">{getModelTypeText(text)}</Tag>,
      },
      {
        title: '行数',
        dataIndex: 'rowCount',
        key: 'rowCount',
        width: 80,
        customRender: (text) => text || '-',
      },
      {
        title: '列数',
        dataIndex: 'columnCount',
        key: 'columnCount',
        width: 80,
        customRender: (text) => text || '-',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        customRender: (text) => <Tag color={getStatusColor(text)}>{getStatusText(text)}</Tag>,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
        customRender: (text) => new Date(text).toLocaleString(),
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
        customRender: ({ record }) => (
          <Space>
            <Tooltip title="查看详情">
              <Button type="link" size="small" onClick={() => showDetailModal(record)}>
                详情
              </Button>
            </Tooltip>
            <Tooltip title="预览数据">
              <Button type="link" size="small" onClick={() => showPreviewModal(record)}>
                预览
              </Button>
            </Tooltip>
            <Tooltip title="编辑">
              <Button type="link" size="small" onClick={() => showEditModal(record)}>
                编辑
              </Button>
            </Tooltip>
            <Tooltip title="删除">
              <Button type="link" size="small" danger onClick={() => handleDelete(record)}>
                删除
              </Button>
            </Tooltip>
          </Space>
        ),
      },
    ];

    // 模型类型选项，与后端枚举保持一致
    const modelTypes = [
      { value: ModelTypeEnum.INNOVATION_HEALTH_MODEL, label: '科创健康性模型' },
      { value: ModelTypeEnum.GENERIC_RISK_MODEL, label: '内控风险模型' },
    ];

    // 方法
    const handleSearch = () => {
      fetchDatasets();
    };

    const handleReset = () => {
      searchText.value = '';
      selectedModelType.value = '';
      fetchDatasets();
    };

    const fetchDatasets = async () => {
      try {
        await datasetStore.fetchDatasets(datasetStore.currentPage, datasetStore.pageSize, {
          search: searchText.value,
          modelType: selectedModelType.value as ModelTypeEnum,
        });
      } catch (error) {
        console.error('获取数据集列表失败:', error);
      }
    };

    const showUploadModal = () => {
      uploadModalVisible.value = true;
    };

    const showDetailModal = (dataset) => {
      selectedDataset.value = dataset;
      detailModalVisible.value = true;
    };

    const showPreviewModal = (dataset) => {
      selectedDataset.value = dataset;
      previewModalVisible.value = true;
    };

    const showEditModal = (dataset) => {
      selectedDataset.value = dataset;
      editModalVisible.value = true;
    };

    const handleDelete = (dataset) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除数据集 "${dataset.name}" 吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await datasetStore.deleteDataset(dataset.id);
            await fetchDatasets();
          } catch (error) {
            console.error('删除数据集失败:', error);
          }
        },
      });
    };

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const getStatusColor = (status) => {
      const statusMap = {
        [DatasetStatus.READY]: 'success',
        [DatasetStatus.PROCESSING]: 'processing',
        [DatasetStatus.ERROR]: 'error',
        [DatasetStatus.UPLOADING]: 'warning',
      };
      return statusMap[status] || 'default';
    };

    const getStatusText = (status) => {
      const statusMap = {
        [DatasetStatus.READY]: '就绪',
        [DatasetStatus.PROCESSING]: '处理中',
        [DatasetStatus.ERROR]: '错误',
        [DatasetStatus.UPLOADING]: '上传中',
      };
      return statusMap[status] || status;
    };

    const getModelTypeText = (modelType) => {
      const typeMap = {
        [ModelTypeEnum.INNOVATION_HEALTH_MODEL]: '科创健康性模型',
        [ModelTypeEnum.GENERIC_RISK_MODEL]: '内控风险模型',
      };
      return typeMap[modelType] || modelType;
    };

    // 生命周期
    onMounted(() => {
      fetchDatasets();
    });

    return () => (
      <div class="p-6">
        <div class="mb-6">
          <h2 style={{ margin: '0 0 8px 0', fontSize: '24px', fontWeight: 600 }}>数据集管理</h2>
          <p style={{ color: '#999', margin: 0 }}>管理机器学习训练数据集，支持多种模型类型的数据验证和预览</p>
        </div>

        {/* 搜索和筛选区域 */}
        <Card class="mb-4">
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                value={searchText.value}
                onChange={(e) => (searchText.value = e.target.value)}
                placeholder="搜索数据集名称或描述"
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                value={selectedModelType.value}
                onChange={(value) => (selectedModelType.value = value)}
                placeholder="选择模型类型"
                allowClear
                style={{ width: '100%' }}
              >
                {modelTypes.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
            <Col span={8} class="text-right">
              <Button type="primary" onClick={showUploadModal}>
                上传数据集
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 数据集列表 */}
        <Card>
          <Table
            dataSource={datasetStore.datasets}
            columns={columns}
            loading={datasetStore.loading}
            pagination={{
              current: datasetStore.currentPage,
              pageSize: datasetStore.pageSize,
              total: datasetStore.totalCount,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                datasetStore.fetchDatasets(page, pageSize, {
                  search: searchText.value,
                  modelType: selectedModelType.value as ModelTypeEnum,
                });
              },
            }}
            scroll={{ x: 1200 }}
          />
        </Card>

        {/* 上传数据集组件 */}
        <DatasetUpload
          visible={uploadModalVisible.value}
          onUpdate:visible={(visible) => (uploadModalVisible.value = visible)}
          onSuccess={() => {
            uploadModalVisible.value = false;
            fetchDatasets();
          }}
        />

        {/* 数据集详情模态框 */}
        <Modal
          title="数据集详情"
          visible={detailModalVisible.value}
          onCancel={() => (detailModalVisible.value = false)}
          footer={null}
          width={800}
        >
          {selectedDataset.value && (
            <div>
              <Row gutter={16}>
                <Col span={12}>
                  <div class="mb-4">
                    <span style={{ fontWeight: 600 }}>名称：</span>
                    <span>{selectedDataset.value.name}</span>
                  </div>
                  <div class="mb-4">
                    <span style={{ fontWeight: 600 }}>描述：</span>
                    <span>{selectedDataset.value.description}</span>
                  </div>
                  <div class="mb-4">
                    <span style={{ fontWeight: 600 }}>模型类型：</span>
                    <span>{selectedDataset.value.modelType}</span>
                  </div>
                </Col>
                <Col span={12}>
                  <div class="mb-4">
                    <span style={{ fontWeight: 600 }}>文件大小：</span>
                    <span>{formatFileSize(selectedDataset.value.fileSize)}</span>
                  </div>
                  <div class="mb-4">
                    <span style={{ fontWeight: 600 }}>行数：</span>
                    <span>{selectedDataset.value.rowCount}</span>
                  </div>
                  <div class="mb-4">
                    <span style={{ fontWeight: 600 }}>列数：</span>
                    <span>{selectedDataset.value.columnCount}</span>
                  </div>
                </Col>
              </Row>
              <div class="mt-4">
                <Button
                  type="primary"
                  onClick={() => {
                    detailModalVisible.value = false;
                    showPreviewModal(selectedDataset.value);
                  }}
                >
                  查看数据预览
                </Button>
              </div>
            </div>
          )}
        </Modal>

        {/* 数据集预览组件 */}
        <DatasetPreview
          visible={previewModalVisible.value}
          dataset={selectedDataset.value}
          onUpdate:visible={(visible) => (previewModalVisible.value = visible)}
        />

        <DatasetEdit
          visible={editModalVisible.value}
          dataset={selectedDataset.value}
          onUpdate:visible={(visible) => (editModalVisible.value = visible)}
          onSuccess={() => {
            fetchDatasets();
            editModalVisible.value = false;
          }}
        />
      </div>
    );
  },
});
