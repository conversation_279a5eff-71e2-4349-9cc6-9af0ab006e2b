import Vue, { type PropType } from 'vue';
import { Form } from 'ant-design-vue';

export default Vue.extend({
  name: 'BaseForm',
  props: {
    model: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    rules: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    labelCol: {
      type: Object as PropType<{ span?: number; offset?: number }>,
      default: () => ({ span: 6 }),
    },
    wrapperCol: {
      type: Object as PropType<{ span?: number; offset?: number }>,
      default: () => ({ span: 18 }),
    },
    layout: {
      type: String as PropType<'horizontal' | 'vertical' | 'inline'>,
      default: 'horizontal',
    },
    labelAlign: {
      type: String as PropType<'left' | 'right'>,
      default: 'right',
    },
    hideRequiredMark: {
      type: Boolean,
      default: false,
    },
    colon: {
      type: Boolean,
      default: true,
    },
    validateOnRuleChange: {
      type: Boolean,
      default: true,
    },
    scrollToFirstError: {
      type: [Boolean, Object] as PropType<boolean | Record<string, any>>,
      default: true,
    },
    validateTrigger: {
      type: [String, Array] as PropType<string | string[]>,
      default: 'change',
    },
    name: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      formRef: null as any,
    };
  },
  methods: {
    // 表单提交成功
    handleFinish(values: any) {
      this.$emit('finish', values);
    },
    // 表单提交失败
    handleFinishFailed({ values, errorFields, outOfDate }: any) {
      this.$emit('finishFailed', { values, errorFields, outOfDate });
    },
    // 表单验证
    handleValidate(...args: any[]) {
      this.$emit('validate', ...args);
    },
    // 表单提交
    handleSubmit(e: Event) {
      this.$emit('submit', e);
    },
    // 暴露方法
    validate() {
      return this.formRef?.validate();
    },
    validateField(nameList: string | string[]) {
      return this.formRef?.validateField(nameList);
    },
    resetFields() {
      this.formRef?.resetFields();
    },
    clearValidate(nameList?: string | string[]) {
      this.formRef?.clearValidate(nameList);
    },
    scrollToField(name: string, options?: Record<string, any>) {
      this.formRef?.scrollToField(name, options);
    },
  },
  render() {
    return (
      <Form
        ref={(el) => (this.formRef = el)}
        model={this.model}
        rules={this.rules}
        labelCol={this.labelCol}
        wrapperCol={this.wrapperCol}
        layout={this.layout}
        labelAlign={this.labelAlign}
        hideRequiredMark={this.hideRequiredMark}
        colon={this.colon}
        validateOnRuleChange={this.validateOnRuleChange}
        scrollToFirstError={this.scrollToFirstError}
        validateTrigger={this.validateTrigger}
        name={this.name}
        onFinish={this.handleFinish}
        onFinishFailed={this.handleFinishFailed}
        onValidate={this.handleValidate}
        onSubmit={this.handleSubmit}
      >
        {this.$slots.default}
      </Form>
    );
  },
});
