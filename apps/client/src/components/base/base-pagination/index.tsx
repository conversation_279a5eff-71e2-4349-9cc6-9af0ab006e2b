import { defineComponent, PropType, ref, watch } from 'vue';
import { Pagination } from 'ant-design-vue';

export default defineComponent({
  name: 'BasePagination',
  props: {
    current: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    total: {
      type: Number,
      default: 0,
    },
    disabled: {
      type: <PERSON><PERSON>an,
      default: false,
    },
    hideOnSinglePage: {
      type: <PERSON>olean,
      default: false,
    },
    pageSizeOptions: {
      type: Array as PropType<string[]>,
      default: () => ['10', '20', '50', '100'],
    },
    showQuickJumper: {
      type: Boolean,
      default: true,
    },
    showSizeChanger: {
      type: Boolean,
      default: true,
    },
    showTotal: {
      type: Function as PropType<(total: number, range: [number, number]) => string>,
      default: (total: number) => `共 ${total} 条`,
    },
    simple: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String as PropType<'default' | 'small'>,
      default: 'default',
    },
  },
  emits: ['update:current', 'update:pageSize', 'change', 'showSizeChange'],
  setup(props, { emit }) {
    const currentPage = ref(props.current);
    const currentPageSize = ref(props.pageSize);

    // 监听props变化
    watch(
      () => props.current,
      (val) => {
        currentPage.value = val;
      }
    );

    watch(
      () => props.pageSize,
      (val) => {
        currentPageSize.value = val;
      }
    );

    // 处理页码变化
    const handleChange = (page: number, pageSize: number) => {
      currentPage.value = page;
      currentPageSize.value = pageSize;
      emit('update:current', page);
      emit('update:pageSize', pageSize);
      emit('change', page, pageSize);
    };

    // 处理每页条数变化
    const handleSizeChange = (current: number, size: number) => {
      currentPage.value = current;
      currentPageSize.value = size;
      emit('update:current', current);
      emit('update:pageSize', size);
      emit('showSizeChange', current, size);
    };

    return () => (
      <Pagination
        current={currentPage.value}
        pageSize={currentPageSize.value}
        total={props.total}
        disabled={props.disabled}
        hideOnSinglePage={props.hideOnSinglePage}
        pageSizeOptions={props.pageSizeOptions}
        showQuickJumper={props.showQuickJumper}
        showSizeChanger={props.showSizeChanger}
        showTotal={props.showTotal}
        simple={props.simple}
        size={props.size}
        onChange={handleChange}
        onShowSizeChange={handleSizeChange}
      />
    );
  },
});
