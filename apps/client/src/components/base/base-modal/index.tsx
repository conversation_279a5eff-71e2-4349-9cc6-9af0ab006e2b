import Vue, { type PropType } from 'vue';
import { Modal } from 'ant-design-vue';

export default Vue.extend({
  name: 'BaseModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    width: {
      type: [Number, String],
      default: 520,
    },
    centered: {
      type: Boolean,
      default: false,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    destroyOnClose: {
      type: Boolean,
      default: false,
    },
    footer: {
      type: [Boolean, Array, Object],
      default: undefined,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    okText: {
      type: String,
      default: '确定',
    },
    okType: {
      type: String as PropType<'primary' | 'danger' | 'dashed' | 'ghost' | 'default' | 'link' | 'text'>,
      default: 'primary',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    keyboard: {
      type: <PERSON>olean,
      default: true,
    },
    zIndex: {
      type: Number,
      default: 1000,
    },
    bodyStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
  },
  data() {
    return {
      localVisible: this.visible,
    };
  },
  watch: {
    visible(val) {
      this.localVisible = val;
    },
  },
  methods: {
    // 处理确认事件
    handleOk(e: Event) {
      this.$emit('ok', e);
    },

    // 处理取消事件
    handleCancel(e: Event) {
      this.$emit('update:visible', false);
      this.$emit('cancel', e);
    },
  },
  render() {
    return (
      <Modal
        visible={this.localVisible}
        title={this.title}
        width={this.width}
        centered={this.centered}
        closable={this.closable}
        confirmLoading={this.confirmLoading}
        destroyOnClose={this.destroyOnClose}
        footer={this.footer}
        maskClosable={this.maskClosable}
        okText={this.okText}
        okType={this.okType}
        cancelText={this.cancelText}
        keyboard={this.keyboard}
        zIndex={this.zIndex}
        bodyStyle={this.bodyStyle}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        on={{
          'update:visible': (val: boolean) => {
            this.localVisible = val;
            this.$emit('update:visible', val);
          },
        }}
      >
        {this.$slots.default}
        {this.$slots.footer && <template slot="footer">{this.$slots.footer}</template>}
        {this.$slots.title && <template slot="title">{this.$slots.title}</template>}
      </Modal>
    );
  },
});
