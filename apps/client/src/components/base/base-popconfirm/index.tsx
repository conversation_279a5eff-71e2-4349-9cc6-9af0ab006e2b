import Vue, { type PropType } from 'vue';
import { Popconfirm } from 'ant-design-vue';

export default Vue.extend({
  name: 'BasePopconfirm',
  props: {
    title: {
      type: String,
      required: true,
    },
    okText: {
      type: String,
      default: '确定',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    okType: {
      type: String as PropType<'primary' | 'danger' | 'dashed' | 'ghost' | 'default' | 'link' | 'text'>,
      default: 'primary',
    },
    okButtonProps: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    cancelButtonProps: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    placement: {
      type: String as PropType<
        | 'top'
        | 'left'
        | 'right'
        | 'bottom'
        | 'topLeft'
        | 'topRight'
        | 'bottomLeft'
        | 'bottomRight'
        | 'leftTop'
        | 'leftBottom'
        | 'rightTop'
        | 'rightBottom'
      >,
      default: 'top',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    overlayClassName: {
      type: String,
      default: '',
    },
    overlayStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    icon: {
      type: Object,
      default: null,
    },
  },
  methods: {
    // 处理确认事件
    handleConfirm(e: Event) {
      this.$emit('confirm', e);
    },

    // 处理取消事件
    handleCancel(e: Event) {
      this.$emit('cancel', e);
    },

    // 处理可见性变化事件
    handleVisibleChange(visible: boolean) {
      this.$emit('visibleChange', visible);
    },
  },
  render() {
    return (
      <Popconfirm
        title={this.title}
        okText={this.okText}
        cancelText={this.cancelText}
        okType={this.okType}
        okButtonProps={this.okButtonProps}
        cancelButtonProps={this.cancelButtonProps}
        placement={this.placement}
        disabled={this.disabled}
        overlayClassName={this.overlayClassName}
        overlayStyle={this.overlayStyle}
        icon={this.icon}
        onConfirm={this.handleConfirm}
        onCancel={this.handleCancel}
        onVisibleChange={this.handleVisibleChange}
      >
        {this.$slots.default}
        {this.$slots.title && <template slot="title">{this.$slots.title}</template>}
        {this.$slots.icon && <template slot="icon">{this.$slots.icon}</template>}
      </Popconfirm>
    );
  },
});
