import { Checkbox as ACheckbox } from 'ant-design-vue';
import Vue, { type VNodeData } from 'vue';

/**
 * BaseBinaryCheckbox
 * 支持0、1值
 */
const BaseBinaryCheckbox = Vue.extend({
  name: 'BaseBinaryCheckbox',
  props: {
    /**
     * 值
     */
    value: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    booleanValue() {
      return this.value === 0 ? false : true;
    },
  },
  methods: {
    onChange(e: Event) {
      const target = e.target as HTMLInputElement | null;
      this.$emit('change', target?.checked ? 1 : 0);
    },
  },
  render() {
    const context: VNodeData = {
      props: {
        // ...this.$props,
        checked: this.booleanValue,
      },
      on: {
        change: this.onChange,
      },
      attrs: this.$attrs,
    };
    return <ACheckbox {...context}>{this.$slots.default}</ACheckbox>;
  },
});

export default BaseBinaryCheckbox;
