import { defineComponent, PropType, ref, watch } from 'vue';
import { Select, Spin } from 'ant-design-vue';
import { debounce } from 'radash';

export default defineComponent({
  name: 'FilterSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: undefined,
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    mode: {
      type: String as PropType<'multiple' | 'tags' | undefined>,
      default: undefined,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    options: {
      type: Array as PropType<{ label: string; value: string | number }[]>,
      default: () => [],
    },
    fetchOptions: {
      type: Function as PropType<(search: string) => Promise<{ label: string; value: string | number }[]>>,
      default: null,
    },
    debounceTime: {
      type: Number,
      default: 300,
    },
    fieldNames: {
      type: Object as PropType<{ label: string; value: string }>,
      default: () => ({ label: 'label', value: 'value' }),
    },
  },
  emits: ['update:value', 'change', 'search'],
  setup(props, { emit }) {
    const localValue = ref(props.value);
    const localOptions = ref(props.options);
    const fetching = ref(false);

    // 监听value变化
    watch(
      () => props.value,
      (val) => {
        localValue.value = val;
      }
    );

    // 监听options变化
    watch(
      () => props.options,
      (val) => {
        localOptions.value = val;
      }
    );

    // 处理选择变化
    const handleChange = (value: string | number | (string | number)[]) => {
      localValue.value = value;
      emit('update:value', value);
      emit('change', value);
    };

    // 处理搜索
    const handleSearch = debounce({ delay: props.debounceTime }, async (value: string) => {
      emit('search', value);

      if (props.fetchOptions && value) {
        fetching.value = true;
        try {
          const result = await props.fetchOptions(value);
          localOptions.value = result;
        } catch (error) {
          console.error('Failed to fetch options:', error);
        } finally {
          fetching.value = false;
        }
      }
    });

    return () => (
      <Select
        v-model:value={localValue.value}
        placeholder={props.placeholder}
        mode={props.mode}
        allowClear={props.allowClear}
        showSearch={props.showSearch}
        disabled={props.disabled}
        loading={props.loading || fetching.value}
        options={localOptions.value}
        fieldNames={props.fieldNames}
        filterOption={props.fetchOptions ? false : true}
        onChange={handleChange}
        onSearch={handleSearch}
        notFoundContent={fetching.value ? <Spin size="small" /> : null}
      />
    );
  },
});
