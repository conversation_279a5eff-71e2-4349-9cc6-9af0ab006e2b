import Vue, { type PropType } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>atter<PERSON><PERSON> } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';

// 注册必要的组件
use([
  <PERSON>vas<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON><PERSON>,
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
]);

export default Vue.extend({
  name: 'BaseChart',
  props: {
    option: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    height: {
      type: [String, Number],
      default: '400px',
    },
    width: {
      type: [String, Number],
      default: '100%',
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      chartRef: null as any,
    };
  },
  watch: {
    option: {
      handler(newOption) {
        if (this.chartRef && newOption) {
          this.chartRef.setOption(newOption, true);
        }
      },
      deep: true,
    },
    loading(loading) {
      if (this.chartRef) {
        loading ? this.chartRef.showLoading() : this.chartRef.hideLoading();
      }
    },
  },
  methods: {
    // 处理图表实例
    handleChartInit(chart: any) {
      this.chartRef = chart;
    },
  },
  mounted() {
    if (this.loading && this.chartRef) {
      this.chartRef.showLoading();
    }
  },
  beforeDestroy() {
    if (this.chartRef) {
      this.chartRef.dispose();
    }
  },
  render() {
    return (
      <div style={{ height: this.height, width: this.width }}>
        <VChart
          ref={(el) => (this.chartRef = el)}
          option={this.option}
          autoresize={this.autoResize}
          theme={this.theme}
          onInit={this.handleChartInit}
          style={{ height: '100%', width: '100%' }}
        />
      </div>
    );
  },
});
