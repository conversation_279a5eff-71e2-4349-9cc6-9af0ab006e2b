import Vue, { type PropType } from 'vue';
import { Card } from 'ant-design-vue';

export default Vue.extend({
  name: 'BaseCard',
  props: {
    title: {
      type: String,
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    bodyStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    extra: {
      type: [String, Object],
      default: '',
    },
    size: {
      type: String as PropType<'default' | 'small'>,
      default: 'default',
    },
    hoverable: {
      type: Boolean,
      default: false,
    },
  },
  render() {
    return (
      <Card
        title={this.title}
        loading={this.loading}
        bordered={this.bordered}
        bodyStyle={this.bodyStyle}
        extra={this.extra}
        size={this.size}
        hoverable={this.hoverable}
      >
        {this.$slots.default}
        {this.$slots.extra && <template slot="extra">{this.$slots.extra}</template>}
        {this.$slots.title && <template slot="title">{this.$slots.title}</template>}
        {this.$slots.cover && <template slot="cover">{this.$slots.cover}</template>}
        {this.$slots.actions && <template slot="actions">{this.$slots.actions}</template>}
      </Card>
    );
  },
});
