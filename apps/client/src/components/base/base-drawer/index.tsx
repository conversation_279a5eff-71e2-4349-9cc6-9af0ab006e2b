import Vue, { type PropType } from 'vue';
import { Drawer } from 'ant-design-vue';

export default Vue.extend({
  name: 'BaseDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    width: {
      type: [Number, String],
      default: 378,
    },
    height: {
      type: [Number, String],
      default: 378,
    },
    placement: {
      type: String as PropType<'top' | 'right' | 'bottom' | 'left'>,
      default: 'right',
    },
    closable: {
      type: Boolean,
      default: true,
    },
    destroyOnClose: {
      type: Boolean,
      default: false,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    mask: {
      type: Boolean,
      default: true,
    },
    zIndex: {
      type: Number,
      default: 1000,
    },
    bodyStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    headerStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    drawerStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    footerStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    size: {
      type: String as PropType<'default' | 'large'>,
      default: 'default',
    },
    extra: {
      type: [String, Object],
      default: '',
    },
    footer: {
      type: [Boolean, Object],
      default: undefined,
    },
  },
  data() {
    return {
      localVisible: this.visible,
    };
  },
  watch: {
    visible(val) {
      this.localVisible = val;
    },
  },
  methods: {
    // 处理关闭事件
    handleClose(e: Event) {
      this.$emit('update:visible', false);
      this.$emit('close', e);
    },
  },
  render() {
    return (
      <Drawer
        visible={this.localVisible}
        title={this.title}
        width={this.width}
        height={this.height}
        placement={this.placement}
        closable={this.closable}
        destroyOnClose={this.destroyOnClose}
        maskClosable={this.maskClosable}
        mask={this.mask}
        zIndex={this.zIndex}
        bodyStyle={this.bodyStyle}
        headerStyle={this.headerStyle}
        drawerStyle={this.drawerStyle}
        footerStyle={this.footerStyle}
        size={this.size}
        extra={this.extra}
        footer={this.footer}
        onClose={this.handleClose}
        on={{
          'update:visible': (val: boolean) => {
            this.localVisible = val;
            this.$emit('update:visible', val);
          },
        }}
      >
        {this.$slots.default}
        {this.$slots.footer && <template slot="footer">{this.$slots.footer}</template>}
        {this.$slots.title && <template slot="title">{this.$slots.title}</template>}
        {this.$slots.extra && <template slot="extra">{this.$slots.extra}</template>}
      </Drawer>
    );
  },
});
