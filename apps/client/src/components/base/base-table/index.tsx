import Vue, { type PropType } from 'vue';
import { Table } from 'ant-design-vue';

import './base-table.module.less';

export default Vue.extend({
  name: 'BaseTable',
  props: {
    columns: {
      type: Array as PropType<any[]>,
      required: true,
    },
    dataSource: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: [String, Function] as PropType<string | ((record: any) => string)>,
      default: 'id',
    },
    pagination: {
      type: [Object, Boolean] as PropType<Record<string, any> | boolean>,
      default: () => ({
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number) => `共 ${total} 条`,
        pageSizeOptions: ['10', '20', '50', '100'],
      }),
    },
    size: {
      type: String as PropType<'default' | 'middle' | 'small'>,
      default: 'default',
    },
    bordered: {
      type: Boolean,
      default: false,
    },
    scroll: {
      type: Object as PropType<{ x?: number | string | true; y?: number | string }>,
      default: () => ({}),
    },
    rowSelection: {
      type: Object as PropType<any>,
      default: null,
    },
    onChange: {
      type: Function as PropType<(pagination: any, filters: any, sorter: any) => void>,
      default: null,
    },
  },
  data() {
    return {
      selectedRowKeys: [] as (string | number)[],
    };
  },
  watch: {
    'rowSelection.selectedRowKeys'(newKeys) {
      if (newKeys) {
        this.selectedRowKeys = newKeys;
      }
    },
  },
  methods: {
    // 处理表格变化事件
    handleTableChange(pagination: any, filters: any, sorter: any) {
      this.$emit('change', pagination, filters, sorter);
      if (this.onChange) {
        this.onChange(pagination, filters, sorter);
      }
    },

    // 处理行选择变化
    handleRowSelectionChange(keys: (string | number)[]) {
      this.selectedRowKeys = keys;
      this.$emit('update:selectedRowKeys', keys);
    },

    // 构建行选择配置
    getRowSelection() {
      if (!this.rowSelection) return undefined;

      return {
        ...this.rowSelection,
        onChange: this.handleRowSelectionChange,
        selectedRowKeys: this.selectedRowKeys,
      };
    },
  },
  render() {
    return (
      <div class="base-table">
        <Table
          columns={this.columns}
          dataSource={this.dataSource}
          loading={this.loading}
          rowKey={this.rowKey}
          pagination={this.pagination}
          size={this.size}
          bordered={this.bordered}
          scroll={this.scroll}
          rowSelection={this.getRowSelection()}
          onChange={this.handleTableChange}
          scopedSlots={this.$scopedSlots}
        />
      </div>
    );
  },
});
