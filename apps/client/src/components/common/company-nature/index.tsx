import Vue from 'vue';

/**
 * 企业性质
 */
export const COMPANY_NATURE_MAP = {
  '*********': '国有企业',
  '*********001': '央企',
  '*********002': '央企子公司',
  '*********003': '省管国企',
  '*********004': '市管国企',
  '*********005': '国有全资企业',
  '*********006': '国有独资企业',
  '*********007': '国有控股企业',
  '*********008': '国有实际控制企业',
  '*********': '集体所有制',
  '*********': '联营企业',
  '002006': '民营企业',
  '002002': '港澳台投资企业',
  '002003': '外商投资企业',
  '0': '其他',
};

const CompanyNature = Vue.extend({
  name: 'CompanyNature',
  props: {
    nature: {
      type: String,
      required: false,
    },
  },
  render() {
    if (!this.nature || !COMPANY_NATURE_MAP[this.nature]) {
      return <div>-</div>;
    }
    return <div>{COMPANY_NATURE_MAP[this.nature]}</div>;
  },
});

export default CompanyNature;
