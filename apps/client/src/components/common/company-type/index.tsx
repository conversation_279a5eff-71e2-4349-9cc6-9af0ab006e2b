import Vue from 'vue';

export const COMPANY_TYPES = [
  {
    value: '*********',
    label: '有限责任公司',
  },
  {
    value: '*********',
    label: '股份有限公司',
  },
  {
    value: '001006',
    label: '个人独资企业',
  },
  {
    value: '*********',
    label: '普通合伙',
  },
  {
    value: '*********',
    label: '有限合伙',
  },
  {
    value: '*********',
    label: '股份合作企业',
  },
  {
    value: '001009',
    label: '个体工商户',
  },
  {
    value: '001003',
    label: '机关单位',
  },
  {
    value: '001004',
    label: '事业单位',
  },
  {
    value: '001005',
    label: '社会组织',
  },
  {
    value: '001011',
    label: '律师事务所',
  },
  {
    value: '001016',
    label: '学校',
  },
  {
    value: '001008',
    label: '农民专业合作社（联合社）',
  },
  {
    value: '001015',
    label: '医疗机构',
  },
  {
    value: '0',
    label: '其他',
  },
];

/**
 * 机构类型（对象）
 */
export const COMPANY_TYPE_MAP = COMPANY_TYPES.reduce((acc, curr) => {
  acc[curr.value] = curr.label;
  return acc;
}, {});

const CompanyType = Vue.extend({
  name: 'CompanyType',
  props: {
    type: {
      type: String,
      required: false,
    },
  },
  render() {
    if (!this.type || !COMPANY_TYPE_MAP[this.type]) {
      return <div>-</div>;
    }
    return <div>{COMPANY_TYPE_MAP[this.type]}</div>;
  },
});

export default CompanyType;
