import { defineComponent } from 'vue';

const COMPANY_REGISTRATION_STATUS_CODE_MAP = {
  10: '在业', // 正常
  20: '存续',
  30: '筹建',
  40: '清算',
  50: '迁入',
  60: '迁出',
  70: '停业',
  75: '歇业',
  80: '撤销',
  85: '责令关闭',
  87: '除名',
  90: '吊销',
  92: '仍注册',
  93: '其他',
  94: '已告解散',
  95: '已终止营业地点',
  96: '不再是独立的实体',
  97: '休止活动',
  99: '注销',
  100: '废止',
  101: '废止清算完结',
  102: '废止许可',
  103: '废止许可完结',
  104: '废止认许',
  105: '废止认许完结',
  106: '接管',
  107: '撤回认许',
  108: '撤回认许完结',
  110: '撤销设立',
  111: '撤销完结',
  112: '撤销无需清算',
  113: '撤销许可',
  114: '撤销认许',
  115: '撤销认许完结',
  116: '核准报备',
  117: '核准设立',
  118: '设立但已解散',
  119: '核准许可报备',
  120: '核准许可登记',
  121: '核准认许',
  122: '清理',
  123: '清理完结',
  124: '破产',
  125: '破产清算完结',
  126: '破产程序终结',
  127: '解散',
  128: '解散清算完结',
  129: '重整',
  130: '合并解散',
  131: '终止破产',
  132: '涂销破产',
  133: '核准许可',
  134: '核准登记',
  135: '分割解散',
  136: '废止登记完结',
  137: '废止登记',
  138: '撤销登记完结',
  139: '撤销登记',
  140: '撤回登记完结',
  141: '撤回登记',
  991: '设立',
  992: '设立',
  993: '设立',
  994: '设立',
  995: '设立',
  996: '设立失败',
  997: '设立',
  998: '存续',
  9910: '注销',
  9911: '注销',
  9912: '注销',
};

const CompanyRegistrationStatus = defineComponent({
  name: 'CompanyRegistrationStatus',
  props: {
    statusCode: {
      type: String,
      required: false,
    },
  },
  render() {
    if (!this.statusCode || !COMPANY_REGISTRATION_STATUS_CODE_MAP[this.statusCode]) {
      return <div>-</div>;
    }
    return <div>{COMPANY_REGISTRATION_STATUS_CODE_MAP[this.statusCode]}</div>;
  },
});

export default CompanyRegistrationStatus;
