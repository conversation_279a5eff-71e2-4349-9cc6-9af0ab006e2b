import { Button, Input, message as Message } from 'ant-design-vue';
import { defineComponent, ref } from 'vue';

const JsonInput = defineComponent({
  name: 'JsonInput',
  props: {
    placeholder: {
      type: String,
      required: false,
    },
    value: {
      type: [Object, Array],
      required: false,
    },
  },
  emtis: ['change', 'cancel'],
  setup(props, { emit }) {
    const currentValue = ref();
    try {
      currentValue.value = JSON.stringify(props.value);
    } catch (error) {
      console.warn(error);
    }

    const onSave = () => {
      console.log('onSave', currentValue.value);
      try {
        const json = JSON.parse(currentValue.value);
        emit('change', json);
      } catch (error) {
        Message.error('Invalid JSON');
      }
    };
    const onCancel = () => {
      console.log('onCancel');
    };

    return {
      currentValue,
      onSave,
      onCancel,
    };
  },
  render() {
    return (
      <div class="bg-gray-1 rounded p-3">
        <Input.TextArea placeholder={this.placeholder} v-model={this.currentValue} autoSize />
        <div class="flex gap-2">
          <Button type="default" size="small" onClick={this.onSave}>
            保存
          </Button>
          {/* <Button size="small" onCancel={this.onCancel}>
            取消
          </Button> */}
        </div>
      </div>
    );
  },
});

export default JsonInput;
