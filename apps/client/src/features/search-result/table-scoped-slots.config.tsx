import moment from 'moment';

import CompanyNature from '@/components/common/company-nature';
import CompanyRegistrationStatus from '@/components/common/company-registration-status';
import CompanyType from '@/components/common/company-type';
import CompanyListedStatus from '@/components/common/company-listed-status';

export const TABLE_SCOPED_SLOTS_CONFIG = {
  /** 企业名称 */
  companyName: (companyName: string, record: any) => {
    if (!companyName) {
      return '-';
    }
    if (!record.companyId) {
      return companyName;
    }
    return (
      <a href={`https://www.qcc.com/firm/${record.companyId}.html`} target="_blank">
        {companyName}
      </a>
    );
  },
  /** 法定代表人 */
  operaName: (operaName: string, record: any) => {
    if (!operaName) {
      return '-';
    }
    if (!record.operaId) {
      return operaName;
    }
    return (
      <a href={`https://www.qcc.com/firm/${record.operaId}.html`} target="_blank">
        {operaName}
      </a>
    );
  },
  /** 日期 */
  date: (date: Date | string, record, index, config) => {
    if (!date) {
      return '-';
    }
    const dateString = typeof date === 'string' ? date : date.toISOString();
    return moment(dateString, config.options?.pattern).format(config.options?.format ?? 'YYYY-MM-DD HH:mm:ss');
  },
  /** 公司状态 */
  companyRegistrationStatus: (statusCode: string) => {
    return <CompanyRegistrationStatus statusCode={statusCode} />;
  },
  /** 公司类型 */
  companyType: (type: string) => {
    return <CompanyType type={type} />;
  },
  /** 公司性质 */
  companyNature: (nature: string) => {
    return <CompanyNature nature={nature} />;
  },
  /** 公司上市状态 */
  companyListedStatus: (status: string) => {
    return <CompanyListedStatus status={status} />;
  },
};
