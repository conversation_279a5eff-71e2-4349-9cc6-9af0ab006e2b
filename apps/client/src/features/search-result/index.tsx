import { defineComponent, type PropType } from 'vue';
import { Icon } from 'ant-design-vue';
import { omit } from 'radash';

import { Card, Table } from '@/components/base';
import type { TableRowSelection } from '@/components/base/base-table';

import { TABLE_SCOPED_SLOTS_CONFIG } from './table-scoped-slots.config';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    rowKey: {
      type: String,
      default: 'id',
    },
    dataSource: {
      type: Array as PropType<unknown[]>,
      default: () => [],
    },
    pagination: {
      type: Object as PropType<{ total: number; pageIndex: number; pageSize: number; pageSizeOptions?: string[] }>,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    columns: {
      type: Array as PropType<unknown[]>,
      default: () => [],
    },
    rowSelection: {
      type: Object as PropType<TableRowSelection>,
      required: false,
    },
    scroll: {
      type: Object as PropType<{ x: number | true; y?: number }>,
      required: false,
    },
    /** 搜索标题 */
    title: {
      type: String,
      required: false,
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const onChangePage = (pageIndex: number, pageSize: number, filters, sorter) => {
      emit('change', {
        pageIndex,
        pageSize,
        filters,
        sorter,
      });
    };
    return {
      onChangePage,
    };
  },
  render() {
    const scopedSlots = {
      ...TABLE_SCOPED_SLOTS_CONFIG,
      ...this.$scopedSlots,
    };
    return (
      <Card>
        {/* 标题栏 */}
        {this.title ? (
          <div slot="title" class="flex items-center gap-1">
            {this.title}
          </div>
        ) : (
          <div slot="title" class="flex items-center gap-1">
            <span>共找到</span>
            <em v-show={this.isLoading} class="color-gray-400">
              <Icon type="sync" spin />
            </em>
            <em v-show={!this.isLoading} class="color-red-500 not-italic">
              {this.pagination?.total ?? '-'}
            </em>
            <span>条数据</span>
          </div>
        )}
        {/* 操作栏 */}
        <div slot="extra">{this.$slots.extra}</div>

        <Table
          scroll={this.scroll}
          bordered={true}
          loading={this.isLoading}
          size="middle"
          rowKey={this.rowKey}
          columns={this.columns}
          dataSource={this.dataSource}
          rowSelection={this.rowSelection}
          pagination={this.pagination}
          scopedSlots={scopedSlots}
          on={{ changePage: this.onChangePage }}
        >
          {Object.keys(omit(this.$slots, ['extra'])).map((slotName) => (
            <template slot={slotName} key={slotName}>
              {this.$slots[slotName]}
            </template>
          ))}
        </Table>
      </Card>
    );
  },
});

export default SearchResult;
