import dayjs from 'dayjs';

/**
 * 耗时计算结果类型
 */
export interface DurationResult {
  /** 显示文本 */
  text: string;
  /** 显示类型：normal | running | failed */
  type: 'normal' | 'running' | 'failed';
}

/**
 * 格式化耗时显示
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param status 状态（可选，用于判断进行中/失败状态）
 * @returns 格式化后的耗时结果对象
 */
export const formatDuration = (startTime: string | null, endTime: string | null, status?: string): DurationResult => {
  if (startTime && endTime) {
    const start = dayjs(startTime);
    const end = dayjs(endTime);
    const totalSeconds = end.diff(start, 'second');

    let text: string;
    if (totalSeconds < 60) {
      text = `${totalSeconds}秒`;
    } else if (totalSeconds < 3600) {
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      text = `${minutes}分${seconds}秒`;
    } else {
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      text = `${hours}时${minutes}分`;
    }

    return { text, type: 'normal' };
  } else if (startTime && !endTime) {
    // 已开始但未完成，根据状态判断
    if (status === 'running') {
      return { text: '执行中', type: 'running' };
    } else if (
      ['failed', 'FAILED', 'FAILED_MR', 'FAILED_DEPLOY_RELEASE', 'FAILED_DEPLOY_PROD', 'CANCELLED'].includes(status || '')
    ) {
      return { text: '失败', type: 'failed' };
    }
    return { text: '进行中', type: 'running' };
  }

  return { text: '-', type: 'normal' };
};
