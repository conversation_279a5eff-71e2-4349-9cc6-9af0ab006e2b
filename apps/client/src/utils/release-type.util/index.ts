import { ReleaseTypeEnum, ReleaseTypeEnumMap } from '@/core/enums';

/**
 * 发版类型映射配置
 * 统一管理所有发版类型的显示文本和颜色
 */
export const RELEASE_TYPE_MAP = {
  [ReleaseTypeEnum.REGULAR]: { text: ReleaseTypeEnumMap[ReleaseTypeEnum.REGULAR], color: 'blue' },
  [ReleaseTypeEnum.HOTFIX]: { text: ReleaseTypeEnumMap[ReleaseTypeEnum.HOTFIX], color: 'red' },
  [ReleaseTypeEnum.EMERGENCY]: { text: ReleaseTypeEnumMap[ReleaseTypeEnum.EMERGENCY], color: 'gold' },
};

/**
 * 获取发版类型的显示信息
 * @param releaseType 发版类型
 * @returns 包含文本和颜色的对象
 */
export const getReleaseTypeInfo = (releaseType: string) => {
  return RELEASE_TYPE_MAP[releaseType] || { text: releaseType, color: 'default' };
};
