import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

function processResponse(response: AxiosResponse): AxiosResponse {
  const { strip = true } = response.config;
  if (strip && response.data) {
    return response.data;
  }
  return response;
}

/**
 * Strip interceptor
 */
export function stripInterceptor(instance: AxiosInstance) {
  instance.interceptors.response.use(processResponse, (error) => Promise.reject(error));
}

declare module 'axios' {
  interface AxiosRequestConfig {
    strip?: boolean;
  }
}

declare module '..' {
  interface HttpClient {
    request<T = any, R = AxiosResponse<T>['data'], D = any>(config: AxiosRequestConfig<D>): Promise<R>;
    get<T = any, R = AxiosResponse<T>['data'], D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
    delete<T = any, R = AxiosResponse<T>['data'], D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
    post<T = any, R = AxiosResponse<T>['data'], D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
  }
}
