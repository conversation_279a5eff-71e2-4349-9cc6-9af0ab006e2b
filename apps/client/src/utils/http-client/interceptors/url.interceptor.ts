import type { AxiosInstance, InternalAxiosRequestConfig } from 'axios';
import { template } from 'radash';

function processRequest(config: InternalAxiosRequestConfig) {
  const { tpl, ...rest } = config;
  if (rest.url && tpl) {
    return {
      ...rest,
      url: template(rest.url, tpl),
    };
  }
  return config;
}

/**
 * URL template interceptor
 */
export function urlInterceptor(instance: AxiosInstance) {
  instance.interceptors.request.use(processRequest, (error) => Promise.reject(error));
}

declare module 'axios' {
  interface AxiosRequestConfig {
    tpl?: Record<string, any>;
  }
}
