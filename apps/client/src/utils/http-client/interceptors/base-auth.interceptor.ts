import { message as Message } from 'ant-design-vue';
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosError } from 'axios';

// 扩展Axios请求配置接口，添加skipInterceptor属性
interface CustomRequestConfig extends InternalAxiosRequestConfig {
  skipInterceptor?: string[];
}

// 定义错误响应数据结构
interface ErrorResponseData {
  error?: string;
  message?: string;
}

/**
 * 统一错误处理
 */
const authErrorHandler = (error: AxiosError<ErrorResponseData>) => {
  // 请求被标记为跳过认证拦截器的情况
  const config = error?.response?.config as CustomRequestConfig | undefined;

  if (config?.skipInterceptor?.includes('auth')) {
    Message.error({
      content: error.response?.data?.error || error.response?.data?.message || error.message || '系统错误',
    });
    return Promise.reject(error);
  }

  // 处理网络错误
  if (error?.isAxiosError) {
    const statusCode = error?.response?.status;

    // 未登录或无权限
    if (statusCode !== undefined && [401].includes(statusCode)) {
      // 显示错误提示
      Message.error({
        content: '登录已过期，请重新登录',
        duration: 1,
        onClose: () => {
          // 跳转到登录页
          window.location.href = '/login';
        },
      });

      return Promise.reject(error);
    }

    // 根据状态码显示不同的错误信息
    let errorMsg = '';
    switch (statusCode) {
      case 400:
        errorMsg = error.response?.data?.message || '请求参数错误';
        break;
      case 403:
        errorMsg = '抱歉，您无权操作该功能';
        break;
      case 404:
        errorMsg = '请求的资源不存在';
        break;
      case 500:
        errorMsg = '服务器内部错误';
        break;
      case undefined:
        errorMsg = '网络连接失败，请检查网络';
        break;
      default:
        errorMsg = error.response?.data?.error || error.response?.data?.message || error.message || '未知错误';
    }

    Message.error({
      content: errorMsg,
      duration: 3,
    });
  }

  return Promise.reject(error);
};

/**
 * BaseAuth interceptor
 */
export const baseAuthInterceptor = (instance: AxiosInstance) => {
  instance.interceptors.response.use(undefined, authErrorHandler);
};
