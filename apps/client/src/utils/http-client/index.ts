import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';

export class HttpClient {
  private instance: AxiosInstance;

  constructor(config: AxiosRequestConfig) {
    this.instance = axios.create(config);
  }

  request<T = unknown, R = AxiosResponse<T>, D = unknown>(config: AxiosRequestConfig<D>): Promise<R> {
    return this.instance.request<T, R, D>(config);
  }

  get<T = unknown, R = AxiosResponse<T>, D = unknown>(url: string, config?: AxiosRequestConfig<D>): Promise<R> {
    return this.instance.get<T, R>(url, config);
  }

  delete<T = unknown, R = AxiosResponse<T>, D = unknown>(url: string, config?: AxiosRequestConfig<D>): Promise<R> {
    return this.instance.delete<T, R>(url, config);
  }

  post<T = unknown, R = AxiosResponse<T>, D = unknown>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R> {
    return this.instance.post<T, R, D>(url, data, config);
  }

  put<T = unknown, R = AxiosResponse<T>, D = unknown>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R> {
    return this.instance.put<T, R, D>(url, data, config);
  }

  inject(interceptor: (instance: AxiosInstance, options?) => void, options?: unknown): void {
    interceptor(this.instance, options);
  }
}

// export class HttpClientError extends Error {
//   constructor(
//     message: string,
//     public readonly response: AxiosResponse
//   ) {
//     super(message);
//   }
// }

export * from './interceptors/url.interceptor';
export * from './interceptors/strip.interceptor';
export * from './interceptors/base-auth.interceptor';
export * from './interceptors/token-auth.interceptor';
