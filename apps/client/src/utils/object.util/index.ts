import { isString, mapEntries, trim } from 'radash';

import { isEmptyString } from '../string.util';

/**
 * 将对象的空值转为 undefined
 * 主要用于表单对象的处理
 * @param values
 * @returns
 */
export const convertEmptyValuesToUndefined = (values: Record<string, unknown>) => {
  return mapEntries(values, (key, value) => {
    // 空字符串
    // 将对象中的空字符串值转为 undefined, 例如: `{ name: ' ' }` => `{ name: undefined }`
    if (isEmptyString(value)) {
      return [key, undefined];
    }
    // 字符串
    if (isString(value)) {
      return [key, trim(value)];
    }
    return [key, value];
  });
};
