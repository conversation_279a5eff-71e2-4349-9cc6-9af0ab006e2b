import { IncidentFixStatusEnum } from '@/core/enums';

// 状态对应的序号映射
export const statusOrderMap = {
  [IncidentFixStatusEnum.Created]: 1,
  [IncidentFixStatusEnum.InProgress]: 2,
  [IncidentFixStatusEnum.Fix]: 3,
  [IncidentFixStatusEnum.Verifying]: 4,
  [IncidentFixStatusEnum.Closed]: 5,
};

// 状态对应的中文名称映射
export const statusNameMap = {
  [IncidentFixStatusEnum.Created]: '新建',
  [IncidentFixStatusEnum.InProgress]: '处理中',
  [IncidentFixStatusEnum.Fix]: '修复',
  [IncidentFixStatusEnum.Verifying]: '验证中',
  [IncidentFixStatusEnum.Closed]: '已关闭',
};

// 获取状态对应的图标
export const getStatusIcon = (status: IncidentFixStatusEnum): string => {
  const iconMap = {
    [IncidentFixStatusEnum.Created]: 'exclamation-circle',
    [IncidentFixStatusEnum.InProgress]: 'sync',
    [IncidentFixStatusEnum.Fix]: 'tool',
    [IncidentFixStatusEnum.Verifying]: 'search',
    [IncidentFixStatusEnum.Closed]: 'check-circle',
  };
  return iconMap[status] || 'question-circle';
};

// 获取状态对应的颜色
export const getStatusColor = (status: IncidentFixStatusEnum): string => {
  const colorMap = {
    [IncidentFixStatusEnum.Created]: 'red',
    [IncidentFixStatusEnum.InProgress]: 'orange',
    [IncidentFixStatusEnum.Fix]: 'gold',
    [IncidentFixStatusEnum.Verifying]: 'blue',
    [IncidentFixStatusEnum.Closed]: 'green',
  };
  return colorMap[status] || 'default';
};
