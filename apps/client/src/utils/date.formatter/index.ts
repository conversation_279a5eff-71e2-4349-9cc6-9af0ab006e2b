import moment from 'moment';

/**
 * 将日期格式化为字符串
 * @param dateString
 * @param pattern
 * @param options
 * @returns
 */
export const formatDateToString = (
  dateString?: string | Date,
  pattern = 'YYYY-MM-DD',
  options = { placeholder: '-', x1000: false }
) => {
  // if (!dateString) {
  //   return options.defaultValue;
  // }
  const date = moment(dateString);
  if (!dateString || !date.isValid()) {
    return options.placeholder;
  }
  return date.format(pattern);
};

// formatDateToTimestamp

// formatDateToISO(dateString?: string | Date, pattern = 'YYYY-MM-DD', options = { placeholder: '-', x1000: false, timezone: false })

// formatDateToISOWithTimezone
