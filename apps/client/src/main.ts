import Vue from 'vue';
import { create<PERSON><PERSON>, PiniaVuePlugin } from 'pinia';
import Antd from 'ant-design-vue';

import App from './app';
import router from './router';

// eslint-disable-next-line import/order
// import 'virtual:uno.css';  // 暂时注释掉
import './assets/main.less';

import 'ant-design-vue/dist/antd.css';

Vue.use(PiniaVuePlugin);
Vue.use(Antd);
Vue.config.productionTip = false;

const pinia = createPinia();

new Vue({
  router,
  pinia,
  render: (h) => h(App),
}).$mount('#app');
