import type { PeriodTypeEnum } from '@/core/enums';

export interface DefectDensityEntity {
  /** ID */
  id: number;
  /** Hashkey */
  hashkey: string;
  /** 责任人 */
  owner: string;
  /** 责任人团队 */
  ownerTeam: string;
  /** 创建时间 */
  createDate: string;
  /** 更新时间 */
  updateDate: string;
  /** TB项目 */
  tbProjectName: string;
  /** 周期类型 */
  periodType: PeriodTypeEnum;
  /** 缺陷密度 */
  defectDensityByCode: number;
  /** 代码行数 */
  codelines: number;
  /** Sonarqube缺陷数量 */
  sonarqubeIssueCount: number;
  /** TB缺陷数量 */
  tbBugCount: number;
  /** 日期 */
  dateRange: string;
  /** 区间开始时间 */
  rangeStartDate: string;
  /** 区间结束时间 */
  rangeEndDate: string;
}

export interface AggsDefectDensityEntity
  extends Pick<
    DefectDensityEntity,
    'ownerTeam' | 'dateRange' | 'defectDensityByCode' | 'codelines' | 'sonarqubeIssueCount' | 'tbBugCount'
  > {
  rangeStartDate?: string;
  rangeEndDate?: string;
}
