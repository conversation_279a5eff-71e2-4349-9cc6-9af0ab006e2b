export interface UnittestEntity {
  /** 主键ID */
  id: number;
  /** 数据唯一标识 */
  hashkey: string;
  /** 任务执行状态 */
  taskStatus: 'success' | 'failed';
  /** 月度统计范围 */
  monthRange: string;
  /** 是否用作月度统计的最终数据 */
  isMonthPrimary: number;
  /** 项目名称 */
  projectName: string;
  /** 代码仓库URL */
  repoUrl: string;
  /** CI流水线ID */
  ciPipelineId: string;
  /** CI任务ID */
  ciJobId: string;
  /** CI提交的分支标识 */
  ciCommitRefSlug: string;
  /** 当前提交者的联系方式 */
  currentCommitter: string | null;
  /** 项目负责人的手机号码 */
  projectOwner: string | null;
  /** 相关提交者 */
  relatedCommitters: string | null;
  /** 测试开始时间 */
  startTime: string;
  /** 测试结束时间 */
  endTime: string;
  /** 测试套件总数 */
  totalTestSuites: number;
  /** 测试用例总数 */
  totalTests: number;
  /** 通过的测试套件数 */
  passedTestSuites: number;
  /** 通过的测试用例数 */
  passedTests: number;
  /** 待处理的测试套件数 */
  pendingTestSuites: number;
  /** 待处理的测试用例数 */
  pendingTests: number;
  /** 待完成的测试用例数 */
  todoTests: number;
  /** 运行时错误的测试套件数 */
  runtimeErrorTestSuites: number;
  /** 失败的测试套件数 */
  failedTestSuites: number;
  /** 失败的测试用例数 */
  failedTests: number;
  /** 语句覆盖率，百分比 */
  statementsCoverage: number;
  /** 已覆盖的语句数 */
  statementsCovered: number;
  /** 语句总数 */
  statementsTotal: number;
  /** 分支覆盖率，百分比 */
  branchesCoverage: number;
  /** 已覆盖的分支数 */
  branchesCovered: number;
  /** 分支总数 */
  branchesTotal: number;
  /** 函数覆盖率，百分比 */
  functionsCoverage: number;
  /** 已覆盖的函数数 */
  functionsCovered: number;
  /** 函数总数 */
  functionsTotal: number;
  /** 代码行覆盖率，百分比 */
  linesCoverage: number;
  /** 已覆盖的代码行数 */
  linesCovered: number;
  /** 代码行总数 */
  linesTotal: number;
  /** 测试报告URL */
  reportUrl?: string;
  /** 覆盖率报告URL */
  coverageUrl?: string;
}
