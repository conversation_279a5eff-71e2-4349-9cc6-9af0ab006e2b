export interface IncidentEntity {
  /** ID */
  id: number;

  /** 问题编号 */
  incidentSerialNumber: string;
  /** 严重程度 */
  severity: string;
  /** 项目名称 */
  tbProjectName: string;
  /** 详细描述 */
  description: string;
  /** 所属迭代 */
  tbSprint: string;
  /** 影响范围 */
  affectedScope: string;
  /** 修复状态 */
  status: string;
  /** 责任人，多个责任人以逗号分隔 */
  responsiblePersons: string;
  /** 报告人 */
  reporter: string;
  /** 处理人 */
  processor: string;
  /** 验证人 */
  verifier: string;
  /** 关闭人 */
  closer: string;
  /** 修复类型 */
  fixType: string;
  /** 修复时间 */
  fixedDate: Date;
  /** 修复方案 */
  solution: string;
  /** TB任务号 */
  tbTaskNumber: string;
  /** 审核时间 */
  reviewDate: Date;
  /** 审核参与者 */
  reviewParticipant: string;
  /** 审核结果 */
  reviewResult: string;
  /** 关闭时间 */
  closedDate: Date;
  /** 创建时间 */
  createDate: Date;
  /** 更新时间 */
  updateDate: Date;

  interimFixedDate?: Date;
  /** @deprecated 使用solution和fixType替代 */
  interimSolution?: string;
  /** @deprecated 使用solution和fixType替代 */
  fixedSolution?: string;
  /** @deprecated 使用verifier替代 */
  approver?: string;
}
