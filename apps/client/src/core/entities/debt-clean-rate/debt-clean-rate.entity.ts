import type { PeriodTypeEnum } from '@/core/enums';

export interface DebtCleanRateEntity {
  /** ID */
  id: number;
  /** Hashkey */
  hashkey: string;
  /** 责任人 */
  owner: string;
  /** 责任人团队 */
  ownerTeam: string;
  /** 创建时间 */
  createDate: string;
  /** 更新时间 */
  updateDate: string;
  /** TB项目 */
  tbProjectName: string;
  /** 周期类型 */
  periodType: PeriodTypeEnum;
  /** 债务清理率 */
  debtCleaningRate: number;
  /** 债务清理率（带权重） */
  debtCleaningRateWithWeight: number;
  /** 债务数量 */
  debtCount: number;
  /** 已清理债务数量 */
  debtCleanCount: number;
  /** Sonar技术债数量 */
  sonarDebtCount: number;
  /** Sonar已清理技术债数量 */
  sonarDebtCleanCount: number;
  /** 日期 */
  dateRange: string;
  /** 区间开始时间 */
  rangeStartDate: string;
  /** 区间结束时间 */
  rangeEndDate: string;
  /** 目标数据 */
  targetData?: any;
}

export interface AggsDebtCleanRateEntity
  extends Pick<
    DebtCleanRateEntity,
    | 'ownerTeam'
    | 'dateRange'
    | 'debtCount'
    | 'debtCleanCount'
    | 'debtCleaningRate'
    | 'debtCleaningRateWithWeight'
    | 'sonarDebtCount'
    | 'sonarDebtCleanCount'
  > {
  rangeStartDate?: string;
  rangeEndDate?: string;
  /** TB项目名称 */
  tbProjectName?: string;
  /** 目标数据 */
  targetData?: any;
}
