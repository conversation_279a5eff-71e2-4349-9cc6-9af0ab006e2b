export interface DefectIntroducedMetricsEntity {
  /** ID */
  id: number;
  /** Bug数量 */
  tbBugCount: number;
  /** 代码提交行数 */
  owner: string;
  ownerTeam: string;
  /** TB项目名称 */
  tbProjectName: string;
  /** 日期范围 */
  dateRange: string;
  codelines: number;

  /** 缺陷数量（代码） */
  defectRateByCode: number;
  /** 缺陷数量（任务） */
  defectRateByTask: number;
  /** 已完成任务数 */
  finishedTaskCount: number;
  /** Sonarqube 缺陷数量 */
  sonarqubeIssueCount: number;
  /** Sonarqube 技术债数量 */
  sonarDebtCount: number;
  /** Sonarqube 已清理技术债数量 */
  sonarDebtCleanCount: number;
  /** 目标数据 */
  targetData?: any;
}

export interface AggsDefectIntroducedMetricsEntity
  extends Pick<
    DefectIntroducedMetricsEntity,
    'defectRateByCode' | 'defectRateByTask' | 'tbBugCount' | 'codelines' | 'finishedTaskCount' | 'sonarqubeIssueCount'
  > {}

export interface IncidentIntroducedMetricsEntity
  extends Omit<DefectIntroducedMetricsEntity, 'tbBugCount' | 'defectRateByCode' | 'defectRateByTask' | 'sonarqubeIssueCount'> {
  periodType: string;
  dateRange: string;
  rangeStartDate: string;
  rangeEndDate: string;
  incidentCount: number;
  incidentRateByCode: number;
  incidentRateByTask: number;
  majorIncidentCount: number;
  majorIncidentRateByCode: number;
  majorIncidentRateByTask: number;
}

export interface AggsIncidentIntroducedMetricsEntity
  extends Pick<
    IncidentIntroducedMetricsEntity,
    | 'dateRange'
    | 'incidentCount'
    | 'incidentRateByCode'
    | 'incidentRateByTask'
    | 'majorIncidentCount'
    | 'majorIncidentRateByCode'
    | 'majorIncidentRateByTask'
    | 'codelines'
    | 'finishedTaskCount'
    | 'ownerTeam'
  > {
  dateRange: string;
}
