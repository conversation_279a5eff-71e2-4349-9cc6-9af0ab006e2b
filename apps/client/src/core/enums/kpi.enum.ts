/** TB项目 */
export enum TbProjectEnum {
  Rover = 'Rover',
  Insights = 'Insights',
  Tender = 'Tender',
  CRM = 'CRM',
  QccPro = 'QccPro',
}
export const TbProjectEnumOptions = [
  {
    label: TbProjectEnum.Rover,
    value: TbProjectEnum.Rover,
  },
  {
    label: TbProjectEnum.Insights,
    value: TbProjectEnum.Insights,
  },
  {
    label: TbProjectEnum.Tender,
    value: TbProjectEnum.Tender,
  },
  {
    label: TbProjectEnum.CRM,
    value: TbProjectEnum.CRM,
  },
  {
    label: TbProjectEnum.QccPro,
    value: TbProjectEnum.QccPro,
  },
];

/** 修复状态 */
export enum IncidentFixStatusEnum {
  Created = 'Created', // 新建
  InProgress = 'InProgress', // 处理中
  Fix = 'Fix', // 修复
  Verifying = 'Verifying', // 验证中
  Closed = 'Closed', // 已关闭
}

export const IncidentFixStatusEnumMap = {
  [IncidentFixStatusEnum.Created]: '新建',
  [IncidentFixStatusEnum.InProgress]: '处理中',
  [IncidentFixStatusEnum.Fix]: '修复',
  [IncidentFixStatusEnum.Verifying]: '验证中',
  [IncidentFixStatusEnum.Closed]: '已关闭',
};

export const IncidentFixStatusEnumOptions = [
  {
    label: IncidentFixStatusEnumMap[IncidentFixStatusEnum.Created],
    value: IncidentFixStatusEnum.Created,
  },
  {
    label: IncidentFixStatusEnumMap[IncidentFixStatusEnum.InProgress],
    value: IncidentFixStatusEnum.InProgress,
  },
  {
    label: IncidentFixStatusEnumMap[IncidentFixStatusEnum.Fix],
    value: IncidentFixStatusEnum.Fix,
  },
  {
    label: IncidentFixStatusEnumMap[IncidentFixStatusEnum.Verifying],
    value: IncidentFixStatusEnum.Verifying,
  },
  {
    label: IncidentFixStatusEnumMap[IncidentFixStatusEnum.Closed],
    value: IncidentFixStatusEnum.Closed,
  },
];

/** 严重程度 */
export enum IncidentSeverityEnum {
  P0 = 'P0',
  P1 = 'P1',
  P2 = 'P2',
  P3 = 'P3',
  P4 = 'P4',
}

export const IncidentSeverityEnumMap = {
  [IncidentSeverityEnum.P0]: 'P0-严重',
  [IncidentSeverityEnum.P1]: 'P1-高',
  [IncidentSeverityEnum.P2]: 'P2-中',
  [IncidentSeverityEnum.P3]: 'P3-低',
  [IncidentSeverityEnum.P4]: 'P4-轻微',
};

export const IncidentSeverityEnumOptions = [
  {
    label: 'P0-严重（系统完全不可用）',
    value: IncidentSeverityEnum.P0,
  },
  {
    label: 'P1-高（主要功能不可用）',
    value: IncidentSeverityEnum.P1,
  },
  {
    label: 'P2-中（功能部分受影响）',
    value: IncidentSeverityEnum.P2,
  },
  {
    label: 'P3-低（小范围影响）',
    value: IncidentSeverityEnum.P3,
  },
  {
    label: 'P4-轻微（几乎无影响）',
    value: IncidentSeverityEnum.P4,
  },
];

/** 团队 */
export enum TeamEnum {
  Frontend = 'frontend',
  Backend = 'backend',
  Full = 'full',
}
const TeamEnumMap = {
  [TeamEnum.Frontend]: '前端',
  [TeamEnum.Backend]: '后端',
  [TeamEnum.Full]: '全部',
};
export const TeamEnumOptions = [
  // {
  //   label: TeamEnumMap[TeamEnum.Full],
  //   value: TeamEnum.Full,
  // },
  {
    label: TeamEnumMap[TeamEnum.Frontend],
    value: TeamEnum.Frontend,
  },
  {
    label: TeamEnumMap[TeamEnum.Backend],
    value: TeamEnum.Backend,
  },
];

/** 修复类型 */
export enum IncidentFixTypeEnum {
  NotFixed = 'NotFixed', // 未修复
  Interim = 'Interim', // 临时修复
  Permanent = 'Permanent', // 彻底修复
}

export const IncidentFixTypeEnumMap = {
  [IncidentFixTypeEnum.NotFixed]: '未修复',
  [IncidentFixTypeEnum.Interim]: '临时修复',
  [IncidentFixTypeEnum.Permanent]: '彻底修复',
};

export const IncidentFixTypeEnumOptions = [
  {
    label: IncidentFixTypeEnumMap[IncidentFixTypeEnum.NotFixed],
    value: IncidentFixTypeEnum.NotFixed,
  },
  {
    label: IncidentFixTypeEnumMap[IncidentFixTypeEnum.Interim],
    value: IncidentFixTypeEnum.Interim,
  },
  {
    label: IncidentFixTypeEnumMap[IncidentFixTypeEnum.Permanent],
    value: IncidentFixTypeEnum.Permanent,
  },
];

/** 统计周期 */
export enum PeriodTypeEnum {
  Month = 'Month',
  Quarter = 'Quarter',
}

export const PeriodTypeEnumMap = {
  [PeriodTypeEnum.Month]: '月',
  [PeriodTypeEnum.Quarter]: '季',
};

export const PeriodTypeEnumOptions = [
  {
    label: PeriodTypeEnumMap[PeriodTypeEnum.Month],
    value: PeriodTypeEnum.Month,
  },
  {
    label: PeriodTypeEnumMap[PeriodTypeEnum.Quarter],
    value: PeriodTypeEnum.Quarter,
  },
];

export enum TaskDataType {
  Debt = 0,
  Task = 1,
}

/** 发版类型 */
export enum ReleaseTypeEnum {
  REGULAR = 'REGULAR', // 常规发版
  HOTFIX = 'HOTFIX', // 紧急修复
  EMERGENCY = 'EMERGENCY', // 应急发版
  // TEST = 'TEST', // 测试发版 暂时也改为 REGULAR 后续会废弃
  // OTHER = 'OTHER', // 其他
}

export const ReleaseTypeEnumMap = {
  [ReleaseTypeEnum.REGULAR]: '常规发版',
  [ReleaseTypeEnum.HOTFIX]: '紧急修复',
  [ReleaseTypeEnum.EMERGENCY]: '应急发版',
  // [ReleaseTypeEnum.TEST]: '测试发版',
  // [ReleaseTypeEnum.OTHER]: '其他',
};

export const ReleaseTypeEnumOptions = [
  {
    label: ReleaseTypeEnumMap[ReleaseTypeEnum.REGULAR],
    value: ReleaseTypeEnum.REGULAR,
  },
  {
    label: ReleaseTypeEnumMap[ReleaseTypeEnum.HOTFIX],
    value: ReleaseTypeEnum.HOTFIX,
  },
  {
    label: ReleaseTypeEnumMap[ReleaseTypeEnum.EMERGENCY],
    value: ReleaseTypeEnum.EMERGENCY,
  },
  // {
  //   label: ReleaseTypeEnumMap[ReleaseTypeEnum.TEST],
  //   value: ReleaseTypeEnum.TEST,
  // },
  // {
  //   label: ReleaseTypeEnumMap[ReleaseTypeEnum.OTHER],
  //   value: ReleaseTypeEnum.OTHER,
  // },
];

/** 发布状态 */
export enum ReleaseStatusEnum {
  PLANNING = 'PLANNING', // 计划中
  IN_PROGRESS = 'IN_PROGRESS', // 发版中
  DEPLOYED_RELEASE = 'DEPLOYED_RELEASE', // 预发环境
  WAITING_APPROVAL = 'WAITING_APPROVAL', // 等待审核
  COMPLETED = 'COMPLETED', // 已完成
  COMPLETED_PARTIAL = 'COMPLETED_PARTIAL', // 部分成功
  FAILED = 'FAILED', // 发版失败
  FAILED_MR = 'FAILED_MR', // MR失败
  FAILED_DEPLOY_RELEASE = 'FAILED_DEPLOY_RELEASE', // 发布环境部署失败
  FAILED_DEPLOY_PROD = 'FAILED_DEPLOY_PROD', // 生产环境部署失败
  ROLLBACK = 'ROLLBACK', // 已回滚
  CANCELLED = 'CANCELLED', // 已取消
}

export const ReleaseStatusEnumMap = {
  [ReleaseStatusEnum.PLANNING]: '计划中',
  [ReleaseStatusEnum.IN_PROGRESS]: '发版中',
  [ReleaseStatusEnum.DEPLOYED_RELEASE]: '预发环境',
  [ReleaseStatusEnum.WAITING_APPROVAL]: '等待审核',
  [ReleaseStatusEnum.COMPLETED]: '已完成',
  [ReleaseStatusEnum.COMPLETED_PARTIAL]: '部分成功',
  [ReleaseStatusEnum.FAILED]: '发版失败',
  [ReleaseStatusEnum.FAILED_MR]: 'MR失败',
  [ReleaseStatusEnum.FAILED_DEPLOY_RELEASE]: '预发部署失败',
  [ReleaseStatusEnum.FAILED_DEPLOY_PROD]: '生产部署失败',
  [ReleaseStatusEnum.ROLLBACK]: '已回滚',
  [ReleaseStatusEnum.CANCELLED]: '已取消',
};

export const ReleaseStatusEnumOptions = [
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.PLANNING],
    value: ReleaseStatusEnum.PLANNING,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.IN_PROGRESS],
    value: ReleaseStatusEnum.IN_PROGRESS,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.DEPLOYED_RELEASE],
    value: ReleaseStatusEnum.DEPLOYED_RELEASE,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.WAITING_APPROVAL],
    value: ReleaseStatusEnum.WAITING_APPROVAL,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.COMPLETED],
    value: ReleaseStatusEnum.COMPLETED,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.COMPLETED_PARTIAL],
    value: ReleaseStatusEnum.COMPLETED_PARTIAL,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.FAILED],
    value: ReleaseStatusEnum.FAILED,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.FAILED_MR],
    value: ReleaseStatusEnum.FAILED_MR,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.FAILED_DEPLOY_RELEASE],
    value: ReleaseStatusEnum.FAILED_DEPLOY_RELEASE,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.FAILED_DEPLOY_PROD],
    value: ReleaseStatusEnum.FAILED_DEPLOY_PROD,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.ROLLBACK],
    value: ReleaseStatusEnum.ROLLBACK,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.CANCELLED],
    value: ReleaseStatusEnum.CANCELLED,
  },
];
export const ReleaseStatusEnumOptionsForFilter = [
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.PLANNING],
    value: ReleaseStatusEnum.PLANNING,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.IN_PROGRESS],
    value: ReleaseStatusEnum.IN_PROGRESS,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.DEPLOYED_RELEASE],
    value: ReleaseStatusEnum.DEPLOYED_RELEASE,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.WAITING_APPROVAL],
    value: ReleaseStatusEnum.WAITING_APPROVAL,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.COMPLETED],
    value: ReleaseStatusEnum.COMPLETED,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.COMPLETED_PARTIAL],
    value: ReleaseStatusEnum.COMPLETED_PARTIAL,
  },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.FAILED],
    value: ReleaseStatusEnum.FAILED,
  },
  // {
  //   label: ReleaseStatusEnumMap[ReleaseStatusEnum.FAILED_MR],
  //   value: ReleaseStatusEnum.FAILED_MR,
  // },
  // {
  //   label: ReleaseStatusEnumMap[ReleaseStatusEnum.FAILED_DEPLOY_RELEASE],
  //   value: ReleaseStatusEnum.FAILED_DEPLOY_RELEASE,
  // },
  // {
  //   label: ReleaseStatusEnumMap[ReleaseStatusEnum.FAILED_DEPLOY_PROD],
  //   value: ReleaseStatusEnum.FAILED_DEPLOY_PROD,
  // },
  // {
  //   label: ReleaseStatusEnumMap[ReleaseStatusEnum.ROLLBACK],
  //   value: ReleaseStatusEnum.ROLLBACK,
  // },
  {
    label: ReleaseStatusEnumMap[ReleaseStatusEnum.CANCELLED],
    value: ReleaseStatusEnum.CANCELLED,
  },
];

/** 发布环境 */
export enum ReleaseEnvironmentEnum {
  PRODUCTION = 'production', // 生产环境
  TESTING = 'testing', // 测试环境
}

export const ReleaseEnvironmentEnumMap = {
  [ReleaseEnvironmentEnum.PRODUCTION]: '生产环境',
  [ReleaseEnvironmentEnum.TESTING]: '测试环境',
};

export const ReleaseEnvironmentEnumOptions = [
  {
    label: ReleaseEnvironmentEnumMap[ReleaseEnvironmentEnum.TESTING],
    value: ReleaseEnvironmentEnum.TESTING,
  },
  {
    label: ReleaseEnvironmentEnumMap[ReleaseEnvironmentEnum.PRODUCTION],
    value: ReleaseEnvironmentEnum.PRODUCTION,
  },
];
