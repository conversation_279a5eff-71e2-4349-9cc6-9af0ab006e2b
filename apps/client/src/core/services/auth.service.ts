import { BaseService } from './base.service';

export class AuthService extends BaseService {
  // 登录
  async login(username: string, password: string) {
    // 暂时不加密密码，直接发送明文（后端使用明文验证）
    return this.httpClient.post('/auth/login', { username, password });
  }

  // 登出
  async logout() {
    return this.httpClient.post('/auth/logout');
  }

  // 测试登录状态
  async testLogin() {
    return this.httpClient.get('/auth/test');
  }
}
