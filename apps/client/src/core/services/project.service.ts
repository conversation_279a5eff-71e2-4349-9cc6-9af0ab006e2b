import type { HttpClient } from '@/utils/http-client';

import { BaseService } from './base.service';

// 项目相关的类型定义
export interface GitProjectInfo {
  id: number;
  projectName: string;
  projectUrl?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  members?: GitProjectMember[];
}

export interface GitProjectMember {
  id: number;
  projectId: number;
  memberEmail: string;
  memberName?: string;
  role?: string;
  createdAt: string;
}

export interface SearchGitProjectsRequest {
  projectName?: string;
  description?: string;
}

export interface AddGitProjectRequest {
  projectName: string;
}

export interface UpdateProjectInfoRequest {
  id: number;
  projectName?: string;
  description?: string;
}

export class ProjectService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH_PROJECTS = '/api/data/projects/search';
  static readonly ADD_GIT_PROJECT = '/api/data/projects/add';
  static readonly UPDATE_PROJECT_INFO = '/api/data/projects/update';
  static readonly GET_PROJECT_DETAILS = '/api/data/projects/details';
  static readonly SYNC_ALL_PROJECT_MEMBERS = '/api/data/projects/members/syncAll';
  static readonly SYNC_PROJECT_MEMBERS = '/api/data/projects/members/sync';

  // 搜索项目列表
  searchProjects = (request: SearchGitProjectsRequest) => {
    return this.httpClient.post<GitProjectInfo[]>(ProjectService.SEARCH_PROJECTS, request);
  };

  // 添加Git项目
  addGitProject = (request: AddGitProjectRequest) => {
    return this.httpClient.post<GitProjectInfo>(ProjectService.ADD_GIT_PROJECT, request);
  };

  // 更新项目信息
  updateProjectInfo = (request: UpdateProjectInfoRequest) => {
    return this.httpClient.post<GitProjectInfo>(ProjectService.UPDATE_PROJECT_INFO, request);
  };

  // 获取项目详情
  getProjectDetails = (id: number) => {
    return this.httpClient.get<GitProjectInfo>(`${ProjectService.GET_PROJECT_DETAILS}/${id}`);
  };

  // 同步所有项目成员
  syncAllProjectMembers = () => {
    return this.httpClient.get<void>(ProjectService.SYNC_ALL_PROJECT_MEMBERS);
  };

  // 同步项目成员
  syncProjectMembers = (id: number) => {
    return this.httpClient.get<void>(`${ProjectService.SYNC_PROJECT_MEMBERS}/${id}`);
  };
}
