import type {
  IncidentFixStatusEnum,
  IncidentSeverityEnum,
  PeriodTypeEnum,
  TaskDataType,
  TbProjectEnum,
  TeamEnum,
} from '../enums';

export type IncidentSearchDto = {
  /** TB项目 */
  tbProject?: TbProjectEnum;
  /** 修复状态 */
  status?: IncidentFixStatusEnum;
  /** 严重程度 */
  severity?: IncidentSeverityEnum;
  /** 负责人 */
  primaryOwner?: string;
};

export type DefectRateSearchDto = {
  /** TB项目 */
  tbProject?: TbProjectEnum;
  /** 人员 */
  employee?: string[];
  /** 团队 */
  employeeTeam?: TeamEnum;
  /** 最近几个统计周期 */
  recentPeriod?: number;
  /** 统计周期 */
  periodType?: PeriodTypeEnum;
};

export type DefectRateAggsSearchDto = Pick<DefectRateSearchDto, 'tbProject' | 'recentPeriod' | 'periodType'>;

export type DebtCleanRateSearchDto = DefectRateSearchDto;
export type DebtCleanRateAggsSearchDto = Pick<DebtCleanRateSearchDto, 'tbProject' | 'recentPeriod' | 'periodType'>;

export type DefectDensitySearchDto = {
  /** TB项目 */
  tbProject?: TbProjectEnum;
  /** 人员 */
  employee?: string[];
  /** 团队 */
  employeeTeam?: TeamEnum;
  /** 最近几个统计周期 */
  recentPeriod?: number;
  /** 统计周期 */
  periodType?: PeriodTypeEnum;
};

export type DefectDensityAggsSearchDto = Pick<DefectDensitySearchDto, 'tbProject' | 'recentPeriod' | 'periodType'>;

export type CleanTaskDataDto = {
  /** TB项目 */
  tbProject: TbProjectEnum;
  /** 数据类型 */
  dataType: TaskDataType;
};
