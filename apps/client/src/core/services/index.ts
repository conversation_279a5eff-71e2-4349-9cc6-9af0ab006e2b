import { HttpClient } from './http-client';
import { DatasetService, TrainingService, ModelService, DeploymentService, UserService } from './api.service';

// 创建 HTTP 客户端实例
const httpClient = new HttpClient({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
});

// 创建服务实例
export const datasetService = new DatasetService(httpClient);
export const trainingService = new TrainingService(httpClient);
export const modelService = new ModelService(httpClient);
export const deploymentService = new DeploymentService(httpClient);
export const userService = new UserService(httpClient);

// 导出服务类
export { HttpClient } from './http-client';
export { DatasetService, TrainingService, ModelService, DeploymentService, UserService } from './api.service';
