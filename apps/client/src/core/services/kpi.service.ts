import type { HttpClient } from '@/utils/http-client';
import type {
  AggsDefectIntroducedMetricsEntity,
  AggsIncidentIntroducedMetricsEntity,
  DefectIntroducedMetricsEntity,
  DebtCleanRateEntity,
  AggsDebtCleanRateEntity,
  IncidentEntity,
  IncidentIntroducedMetricsEntity,
  PaginationResponse,
  DefectDensityEntity,
  AggsDefectDensityEntity,
} from '@/core/entities';
import { request } from '@/utils/request';

import { BaseService } from './base.service';
import type {
  DebtCleanRateAggsSearchDto,
  DebtCleanRateSearchDto,
  DefectRateAggsSearchDto,
  DefectRateSearchDto,
  IncidentSearchDto,
  DefectDensitySearchDto,
  DefectDensityAggsSearchDto,
  CleanTaskDataDto,
} from './kpi.dto';
import type { TeamEnum, TbProjectEnum } from '../enums';

// 发布管理相关的类型定义
export interface ProductReleaseEntity {
  id: number;
  releaseHashKey: string;
  tbProjectName: string;
  releaseType: string;
  status: string; // 修复：使用正确的字段名
  releaseNotes?: string;
  plannedReleaseTime: string;
  actualReleaseTime?: string;
  createdBy: string;
  createDate: string; // 修复：使用正确的字段名
  updateDate: string; // 修复：使用正确的字段名
  relatedProjects: string; // 修复：后端是字符串类型，逗号分隔
  projectRelations?: any[]; // 关联的项目发版关系
}

export interface ProjectReleaseEntity {
  id: number;
  projectName: string;
  sourceBranch: string;
  targetBranch: string;
  status: string;
  releaseType: string;
  createdBy: string;
  createDate: string;
  completedAt?: string;
  plannedStartTime?: string;
  actualStartTime?: string;
  releaseNotes?: string;
  incidentNumber?: number;
}

// 搜索项目响应类型
export interface SearchProductProjectResponseItem {
  projectName: string;
  projectReleases?: ProjectReleaseEntity[];
}

// 关联项目模型
export interface RelaseProjectItemModel {
  projectReleaseId?: number;
  projectName: string;
  sourceBranch: string;
  incidentIds?: number[];
}

export interface CreateProductReleaseDto {
  tbProjectName: string;
  releaseType: string;
  releaseEnvironment: string;
  relatedProjects: RelaseProjectItemModel[];
  releaseNotes?: string;
  plannedReleaseTime: string;
}

export interface SearchProductReleaseDto {
  pageIndex?: number;
  pageSize?: number;
  tbProjectName?: string;
  projectName?: string;
  releaseType?: string;
  releaseStatus?: string;
  createdBy?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: string;
}

export interface SearchProjectReleaseDto {
  pageIndex?: number;
  pageSize?: number;
  tbProjectName?: string;
  projectName?: string;
  statusList?: string[];
  releaseTypeList?: string[];
  createdBy?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: string;
}

export interface SearchProductRelatedProjectDto {
  tbProjectName: string;
  projectName?: string | undefined;
}

export interface SearchGitBranchDto {
  projectName: string;
  branchName: string;
}

// GitLab分支搜索响应模型
export interface GitlabCommitModel {
  id: string;
  short_id: string;
  title: string;
  message: string;
  author_name: string;
  author_email: string;
  authored_date: string;
  committer_name: string;
  committer_email: string;
  committed_date: string;
  created_at: string;
  parent_ids: string[];
  web_url: string;
}

export interface GitlabBranchSearchResponseItemModel {
  name: string;
  commit: GitlabCommitModel;
  merged: boolean;
  protected: boolean;
  developers_can_push: boolean;
  developers_can_merge: boolean;
  can_push: boolean;
  default: boolean;
  web_url: string;
}

export interface BranchOption {
  name: string;
}

// 每个团队类型的指标数据
export type TeamDefectIntroducedMetrics<T> = {
  [TeamEnum.Backend]: T;
  [TeamEnum.Frontend]: T;
  [TeamEnum.Full]: T;
};

/** 缺陷引入率(团队)聚合搜索 */
export interface GenericIntroducedRateResponse<T> {
  current: TeamDefectIntroducedMetrics<T>;
  average: TeamDefectIntroducedMetrics<T>;
  trend: {
    [key in TeamEnum]: T[];
  };
}

type AggsDefectIntroducedRateResponse = GenericIntroducedRateResponse<AggsDefectIntroducedMetricsEntity>;

type AggsIncidentIntroducedRateResponse = GenericIntroducedRateResponse<AggsIncidentIntroducedMetricsEntity>;

type AggsDebtCleanRateResponse = GenericIntroducedRateResponse<AggsDebtCleanRateEntity>;

type AggsDefectDensityResponse = GenericIntroducedRateResponse<AggsDefectDensityEntity>;

class IncidentService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/api/data/incident/search';
  static readonly GET_DETAIL = '/api/data/incident';
  static readonly CREATE = '/api/data/incident/create';
  static readonly CREATE_WITH_STATUS = '/api/data/incident/create-with-status';
  static readonly START_PROCESS = '/api/data/incident/start-process';
  static readonly FIX = '/api/data/incident/fix';
  static readonly VERIFY = '/api/data/incident/verify';
  static readonly REVIEW = '/api/data/incident/review';
  static readonly CLOSE = '/api/data/incident/close';
  static readonly UPDATE = '/api/data/incident/update';

  search = (data: IncidentSearchDto) => {
    return this.httpClient.post<PaginationResponse<IncidentEntity[]>>(IncidentService.SEARCH, data);
  };

  getDetail = (id: number) => {
    return this.httpClient.get<IncidentEntity>(`${IncidentService.GET_DETAIL}/${id}`);
  };

  create = (data: any) => {
    return this.httpClient.post(IncidentService.CREATE, data);
  };

  createWithStatus = (data: any) => {
    return this.httpClient.post(IncidentService.CREATE_WITH_STATUS, data);
  };

  startProcess = (data: any) => {
    return this.httpClient.post(IncidentService.START_PROCESS, data);
  };

  fix = (data: any) => {
    return this.httpClient.post(IncidentService.FIX, data);
  };

  verify = (data: any) => {
    return this.httpClient.post(IncidentService.VERIFY, data);
  };

  review = (data: any) => {
    return this.httpClient.post(IncidentService.REVIEW, data);
  };

  close = (data: any) => {
    return this.httpClient.post(IncidentService.CLOSE, data);
  };

  update = (data: any) => {
    return this.httpClient.post(IncidentService.UPDATE, data);
  };
}

class MetricService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH_DEFECT_RATE = '/api/metrics/search/defectRate';
  static readonly SEARCH_AGGS_DEFECT_RATE = '/api/metrics/aggs/defectRate';

  static readonly SEARCH_INCIDENT_RATE = '/api/metrics/search/incidentRate';
  static readonly SEARCH_AGGS_INCIDENT_RATE = '/api/metrics/aggs/incidentRate';

  static readonly SEARCH_DEBT_CLEAN_RATE = '/api/metrics/search/debtCleanRate';
  static readonly SEARCH_AGGS_DEBT_CLEAN_RATE = '/api/metrics/aggs/debtCleanRate';

  static readonly SEARCH_DEFECT_DENSITY = '/api/metrics/search/defectDensity';
  static readonly SEARCH_AGGS_DEFECT_DENSITY = '/api/metrics/aggs/defectDensity';

  /** 缺陷引入率 */
  searchDefectRate = (data: DefectRateSearchDto) => {
    return this.httpClient.post<PaginationResponse<DefectIntroducedMetricsEntity[]>>(MetricService.SEARCH_DEFECT_RATE, data);
  };
  searchAggsDefectRate = (data: DefectRateAggsSearchDto) => {
    return this.httpClient.post<AggsDefectIntroducedRateResponse>(MetricService.SEARCH_AGGS_DEFECT_RATE, data);
  };

  /** 生产事故率 */
  searchIncidentRate = (data: DefectRateSearchDto) => {
    return this.httpClient.post<PaginationResponse<IncidentIntroducedMetricsEntity[]>>(MetricService.SEARCH_INCIDENT_RATE, data);
  };
  searchAggsIncidentRate = (data: DefectRateAggsSearchDto) => {
    return this.httpClient.post<AggsIncidentIntroducedRateResponse>(MetricService.SEARCH_AGGS_INCIDENT_RATE, data);
  };

  /** 技术债清理率 */
  searchDebtCleanRate = (data: DebtCleanRateSearchDto) => {
    return this.httpClient.post<PaginationResponse<DebtCleanRateEntity[]>>(MetricService.SEARCH_DEBT_CLEAN_RATE, data);
  };
  searchAggsDebtCleanRate = (data: DebtCleanRateAggsSearchDto) => {
    return this.httpClient.post<AggsDebtCleanRateResponse>(MetricService.SEARCH_AGGS_DEBT_CLEAN_RATE, data);
  };

  /** 缺陷密度 */
  searchDefectDensity = (data: DefectDensitySearchDto) => {
    return this.httpClient.post<PaginationResponse<DefectDensityEntity[]>>(MetricService.SEARCH_DEFECT_DENSITY, data);
  };
  searchAggsDefectDensity = (data: DefectDensityAggsSearchDto) => {
    return this.httpClient.post<AggsDefectDensityResponse>(MetricService.SEARCH_AGGS_DEFECT_DENSITY, data);
  };
}

class IngestService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }
  static readonly IMPORT_SONAR_DATA = '/api/ingest/sonar/import';
  static readonly IMPORT_CODE_COMMIT_INFO_BY_AUTHOR = '/api/ingest/code-commit/author';
  static readonly IMPORT_CODE_COMMIT_INFO_BY_PROJECT = '/api/ingest/code-commit/project';
  static readonly IMPORT_TB_DATA = '/api/ingest/task';
  static readonly IMPORT_TB_DATA_CLEAN = '/api/ingest/task/clean';
  static readonly ANALYZE_TB_DATA = '/api/metrics/analyze';

  static readonly ANALYZE_DEFECT_RATE = '/metrics/analyze/defectRate';
  static readonly ANALYZE_INCIDENT_RATE = '/metrics/analyze/incidentRate';
  static readonly ANALYZE_DEBT_CLEAN_RATE = '/metrics/analyze/debtCleanRate';
  static readonly ANALYZE_DEFECT_DENSITY = '/metrics/analyze/defectDensity';

  analyze = (
    type: string, //'defectRate' | 'incidentRate' | 'debtCleanRate' | 'defectDensity',
    params: { tbProject?: TbProjectEnum; quarter?: number; month?: number }
  ) => {
    const apiMap = {
      defectRate: IngestService.ANALYZE_DEFECT_RATE,
      incidentRate: IngestService.ANALYZE_INCIDENT_RATE,
      debtCleanRate: IngestService.ANALYZE_DEBT_CLEAN_RATE,
      defectDensity: IngestService.ANALYZE_DEFECT_DENSITY,
    };

    return request.post(apiMap[type], params);
  };

  // 导入Sonar数据
  importSonarData = (data: any) => {
    return this.httpClient.post(IngestService.IMPORT_SONAR_DATA, data);
  };
  // 导入开发者代码提交统计
  importCodeCommitInfoByAuthor = (data: any) => {
    return this.httpClient.post(IngestService.IMPORT_CODE_COMMIT_INFO_BY_AUTHOR, data);
  };
  // 导入项目代码行数统计
  importCodeCommitInfoByProject = (data: any) => {
    return this.httpClient.post(IngestService.IMPORT_CODE_COMMIT_INFO_BY_PROJECT, data);
  };
  // 导入TB数据
  importTbData = (project: TbProjectEnum, content: string) => {
    return this.httpClient.post(`${IngestService.IMPORT_TB_DATA}/${project.toLowerCase()}`, content, {
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  };
  analyzeTbData = (project: TbProjectEnum) => {
    return this.httpClient.post(`${IngestService.ANALYZE_TB_DATA}/${project.toLowerCase()}`);
  };
  // 删除数据
  cleanTbData = (data: CleanTaskDataDto) => {
    return this.httpClient.post(`${IngestService.IMPORT_TB_DATA_CLEAN}`, data);
  };
}

class ReleaseService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly CREATE_PRODUCT_RELEASE = '/api/release/product/create';
  static readonly SEARCH_PRODUCT_RELEASE = '/api/release/product/search';
  static readonly GET_PRODUCT_RELEASE_DETAIL = '/api/release/product';
  static readonly START_PRODUCT_RELEASE = '/api/release/product/start';
  static readonly SEARCH_PRODUCT_PROJECT = '/api/release/product/search-project';
  static readonly CREATE_HOTFIX_RELEASE = '/api/release/hotfix/create';
  static readonly SEARCH_GIT_BRANCH = '/api/release-management/gitlab/branch/search';
  static readonly SEARCH_PROJECT_RELEASE = '/api/release/project/search';
  static readonly GET_PROJECT_RELEASE_DETAIL = '/api/release/project';
  static readonly UPDATE_PRODUCT_RELEASE_INCIDENTS = '/api/release/product/update';

  /** 创建产品发版 */
  createProductRelease = (data: CreateProductReleaseDto) => {
    return this.httpClient.post<ProductReleaseEntity>(ReleaseService.CREATE_PRODUCT_RELEASE, data);
  };

  /** 搜索产品发版 */
  searchProductRelease = (data: SearchProductReleaseDto) => {
    return this.httpClient.post<PaginationResponse<ProductReleaseEntity[]>>(ReleaseService.SEARCH_PRODUCT_RELEASE, data);
  };

  /** 获取产品发版详情 */
  getProductReleaseDetail = (id: number) => {
    return this.httpClient.get<ProductReleaseEntity>(`${ReleaseService.GET_PRODUCT_RELEASE_DETAIL}/${id}`);
  };

  /** 执行产品发版 */
  startProductRelease = (id: number) => {
    return this.httpClient.post(`${ReleaseService.START_PRODUCT_RELEASE}/${id}`);
  };

  /** 搜索产品发版关联的项目 */
  searchProductProject = (data: SearchProductRelatedProjectDto) => {
    return this.httpClient.post<SearchProductProjectResponseItem[]>(ReleaseService.SEARCH_PRODUCT_PROJECT, data);
  };

  /** 搜索Git分支 */
  searchGitBranch = (params: SearchGitBranchDto) => {
    return this.httpClient.post<GitlabBranchSearchResponseItemModel[]>(ReleaseService.SEARCH_GIT_BRANCH, params);
  };

  /** 创建补丁修复 */
  createHotfixRelease = (data: any) => {
    return this.httpClient.post(ReleaseService.CREATE_HOTFIX_RELEASE, data);
  };

  /** 搜索项目发版记录 */
  searchProjectRelease = (data: SearchProjectReleaseDto) => {
    return this.httpClient.post<PaginationResponse<ProjectReleaseEntity[]>>(ReleaseService.SEARCH_PROJECT_RELEASE, data);
  };

  /** 获取项目发版详情 */
  getProjectReleaseDetail = (id: number) => {
    return this.httpClient.get<ProjectReleaseEntity>(`${ReleaseService.GET_PROJECT_RELEASE_DETAIL}/${id}`);
  };

  /** 更新产品发版关联的事故 */
  updateProductReleaseIncidents = (id: number, data: { incidentIds: number[] }) => {
    return this.httpClient.post(`${ReleaseService.UPDATE_PRODUCT_RELEASE_INCIDENTS}/${id}`, data);
  };

  /** 更新产品发版信息 */
  updateProductRelease = (id: number, data: { releaseNotes?: string; plannedReleaseTime?: string; incidentIds?: number[] }) => {
    return this.httpClient.post(`${ReleaseService.UPDATE_PRODUCT_RELEASE_INCIDENTS}/${id}`, data);
  };

  /** 添加项目到产品发版 */
  addProjectToProductRelease = (productReleaseId: number, data: RelaseProjectItemModel) => {
    return this.httpClient.post(`/api/release/product/add-project/${productReleaseId}`, data);
  };

  /** 撤销项目发版 */
  cancelProjectRelease = (projectReleaseId: number) => {
    return this.httpClient.post(`/api/release/project/cancel/${projectReleaseId}`);
  };

  /** 更新项目关联的事故 */
  updateProjectIncidents = (projectReleaseId: number, data: { incidentIds: number[] }) => {
    return this.httpClient.post(`/api/release/project/incident/${projectReleaseId}`, data);
  };
}

export class KpiService {
  metric: MetricService;
  incident: IncidentService;
  ingest: IngestService;
  release: ReleaseService;

  constructor(private httpClient: HttpClient) {
    this.metric = new MetricService(httpClient);
    this.incident = new IncidentService(httpClient);
    this.ingest = new IngestService(httpClient);
    this.release = new ReleaseService(httpClient);
  }
}
