import type { HttpClient } from '@/utils/http-client';

import { BaseService } from './base.service';

// 产品相关的类型定义
export interface TbProjectInfo {
  id: number;
  tbProjectName: string;
  owner: string;
  productOwner: string;
  description?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  relatedProjects?: TbProductProjectRelation[];
}

export interface SearchTbProjectRequest {
  tbProjectName?: string;
  status?: 'active' | 'inactive';
  owner?: string;
  productOwner?: string;
}

export interface UpdateTbProjectRequest {
  owner?: string;
  productOwner?: string;
  description?: string;
  status?: 'active' | 'inactive';
}

export interface TbProductProjectRelation {
  id: number;
  tbProjectInfoId: number;
  gitProjectName: string;
  createdAt: string;
}

export interface AddProjectToProductRequest {
  tbProjectInfoId: number;
  gitProjectName: string;
}

export interface InitializeTbProjectDataRequest {
  overwrite?: boolean;
}

export interface SearchTbProjectResponse {
  id: number;
  tbProjectName: string;
  owner: string;
  productOwner: string;
  description?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  relatedProjects?: TbProductProjectRelation[];
}

export class ProductService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly UPDATE_TB_PROJECT = '/api/data/products/update';
  static readonly SEARCH_TB_PROJECTS = '/api/data/products/search';
  static readonly GET_TB_PROJECT_DETAIL = '/api/data/products';
  static readonly ADD_PROJECT_RELATION = '/api/data/products/relations';
  static readonly REMOVE_PROJECT_RELATION = '/api/data/products/relations';
  static readonly INITIALIZE_TB_PROJECT_DATA = '/api/data/products/initialize';

  // 更新TB项目
  updateTbProject = (id: number, request: UpdateTbProjectRequest) => {
    return this.httpClient.post<TbProjectInfo>(`${ProductService.UPDATE_TB_PROJECT}/${id}`, request);
  };

  // 搜索TB项目
  searchTbProjects = (request: SearchTbProjectRequest) => {
    return this.httpClient.post<SearchTbProjectResponse[]>(ProductService.SEARCH_TB_PROJECTS, request);
  };

  // 获取TB项目详情
  getTbProjectDetail = (id: number) => {
    return this.httpClient.get<TbProjectInfo>(`${ProductService.GET_TB_PROJECT_DETAIL}/${id}`);
  };

  // 添加项目关系
  addProjectRelation = (request: AddProjectToProductRequest) => {
    return this.httpClient.post<TbProductProjectRelation>(ProductService.ADD_PROJECT_RELATION, request);
  };

  // 删除项目关系
  removeProjectRelation = (id: number) => {
    return this.httpClient.delete<{ message: string }>(`${ProductService.REMOVE_PROJECT_RELATION}/${id}`);
  };

  // 初始化TB项目数据
  initializeTbProjectData = (request: InitializeTbProjectDataRequest) => {
    return this.httpClient.post<{ message: string; count: number }>(ProductService.INITIALIZE_TB_PROJECT_DATA, request);
  };
}
