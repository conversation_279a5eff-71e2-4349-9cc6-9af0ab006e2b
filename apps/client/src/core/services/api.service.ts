import { HttpClient } from './http-client';

/**
 * 基础服务类，所有服务类都应该继承这个类
 */
export class BaseService {
  protected httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }
}

/**
 * 数据集服务
 */
export class DatasetService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/datasets/search';
  static readonly GET_DETAIL = '/datasets';
  static readonly CREATE = '/datasets/create';
  static readonly UPDATE = '/datasets/update';
  static readonly DELETE = '/datasets/delete';
  static readonly PREVIEW = '/datasets/preview';

  search = (params: any) => {
    return this.httpClient.get(DatasetService.SEARCH, { params });
  };

  getDetail = (id: number) => {
    return this.httpClient.get(`${DatasetService.GET_DETAIL}/${id}`);
  };

  create = (data: any) => {
    return this.httpClient.post(DatasetService.CREATE, data);
  };

  update = (id: number, data: any) => {
    return this.httpClient.put(`${DatasetService.UPDATE}/${id}`, data);
  };

  delete = (id: number) => {
    return this.httpClient.delete(`${DatasetService.DELETE}/${id}`);
  };

  preview = (id: number) => {
    return this.httpClient.get(`${DatasetService.PREVIEW}/${id}`);
  };
}

/**
 * 训练任务服务
 */
export class TrainingService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/training/search';
  static readonly GET_DETAIL = '/training';
  static readonly CREATE = '/training/create';
  static readonly UPDATE = '/training/update';
  static readonly DELETE = '/training/delete';
  static readonly START = '/training/start';
  static readonly STOP = '/training/stop';
  static readonly METRICS = '/training/metrics';

  search = (params: any) => {
    return this.httpClient.get(TrainingService.SEARCH, { params });
  };

  getDetail = (id: number) => {
    return this.httpClient.get(`${TrainingService.GET_DETAIL}/${id}`);
  };

  create = (data: any) => {
    return this.httpClient.post(TrainingService.CREATE, data);
  };

  update = (id: number, data: any) => {
    return this.httpClient.put(`${TrainingService.UPDATE}/${id}`, data);
  };

  delete = (id: number) => {
    return this.httpClient.delete(`${TrainingService.DELETE}/${id}`);
  };

  start = (id: number) => {
    return this.httpClient.post(`${TrainingService.START}/${id}`);
  };

  stop = (id: number) => {
    return this.httpClient.post(`${TrainingService.STOP}/${id}`);
  };

  getMetrics = (id: number) => {
    return this.httpClient.get(`${TrainingService.METRICS}/${id}`);
  };
}

/**
 * 模型服务
 */
export class ModelService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/models/search';
  static readonly GET_DETAIL = '/models';
  static readonly DELETE = '/models/delete';
  static readonly EVALUATE = '/models/evaluate';
  static readonly COMPARE = '/models/compare';

  search = (params: any) => {
    return this.httpClient.get(ModelService.SEARCH, { params });
  };

  getDetail = (id: number) => {
    return this.httpClient.get(`${ModelService.GET_DETAIL}/${id}`);
  };

  delete = (id: number) => {
    return this.httpClient.delete(`${ModelService.DELETE}/${id}`);
  };

  evaluate = (id: number, data: any) => {
    return this.httpClient.post(`${ModelService.EVALUATE}/${id}`, data);
  };

  compare = (ids: number[]) => {
    return this.httpClient.post(ModelService.COMPARE, { ids });
  };
}

/**
 * 部署服务
 */
export class DeploymentService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/deployments/search';
  static readonly GET_DETAIL = '/deployments';
  static readonly CREATE = '/deployments/create';
  static readonly DELETE = '/deployments/delete';
  static readonly ROLLBACK = '/deployments/rollback';
  static readonly LOGS = '/deployments/logs';

  search = (params: any) => {
    return this.httpClient.get(DeploymentService.SEARCH, { params });
  };

  getDetail = (id: number) => {
    return this.httpClient.get(`${DeploymentService.GET_DETAIL}/${id}`);
  };

  create = (data: any) => {
    return this.httpClient.post(DeploymentService.CREATE, data);
  };

  delete = (id: number) => {
    return this.httpClient.delete(`${DeploymentService.DELETE}/${id}`);
  };

  rollback = (id: number, version: number) => {
    return this.httpClient.post(`${DeploymentService.ROLLBACK}/${id}`, { version });
  };

  getLogs = (id: number, params: any) => {
    return this.httpClient.get(`${DeploymentService.LOGS}/${id}`, { params });
  };
}

/**
 * 用户服务
 */
export class UserService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly LOGIN = '/auth/login';
  static readonly LOGOUT = '/auth/logout';
  static readonly GET_PROFILE = '/auth/profile';
  static readonly SEARCH = '/users/search';
  static readonly UPDATE_PERMISSIONS = '/users/permissions';

  login = (data: { username: string; password: string }) => {
    return this.httpClient.post(UserService.LOGIN, data);
  };

  logout = () => {
    return this.httpClient.post(UserService.LOGOUT);
  };

  getProfile = () => {
    return this.httpClient.get(UserService.GET_PROFILE);
  };

  search = (params: any) => {
    return this.httpClient.get(UserService.SEARCH, { params });
  };

  updatePermissions = (id: number, permissions: string[]) => {
    return this.httpClient.put(`${UserService.UPDATE_PERMISSIONS}/${id}`, { permissions });
  };
}
