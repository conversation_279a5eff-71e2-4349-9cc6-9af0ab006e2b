import { ref, watch } from 'vue';

/**
 * 控制弹窗显示隐藏
 * @param defaultValue
 * @returns
 */
export const useControlVisible = (defaultValue = false, defaultParams: Record<string, any> = {}) => {
  const visible = ref(defaultValue);
  const params = ref(defaultParams); // 保存弹窗所需参数

  // 更新参数
  const updateParams = (currentParams) => {
    params.value = currentParams;
  };

  // 重置状态
  watch(visible, (value: boolean) => {
    if (!value) {
      updateParams(defaultParams); // Rest
    }
  });

  const open = (payload = {}) => {
    updateParams(payload);
    visible.value = true;
  };
  const close = () => {
    visible.value = false;
  };

  const toggle = () => {
    visible.value = !visible.value;
  };

  return {
    visible,
    open,
    close,
    toggle,
    params,
  };
};
