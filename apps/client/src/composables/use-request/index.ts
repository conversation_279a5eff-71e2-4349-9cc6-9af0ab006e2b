import { computed, ref } from 'vue';

type Request<R = any> = (...args: any[]) => Promise<R>;
type RequestStatus = 'idle' | 'pending' | 'success' | 'error';
type RequestOption = {
  clearBeforeRequest?: boolean; // 请求前是否清空数据
};

export function useRequest<R = any>(request: Request<R>, option?: RequestOption) {
  if (!request) {
    throw new Error('Request 不存在');
  }

  const { clearBeforeRequest = false } = option ?? {};

  const status = ref<RequestStatus>('idle');
  const isInit = ref<boolean>(true); // 是否是初始化状态(request是否被执行过)
  const isIdle = computed(() => status.value === 'idle');
  const isLoading = computed(() => status.value === 'pending');
  const isSuccess = computed(() => status.value === 'success');
  const isError = computed(() => status.value === 'error');

  const data = ref<R | undefined>();
  const error = ref<unknown | undefined>();

  /**
   * 重置状态
   */
  const reset = () => {
    status.value = 'idle';
    data.value = undefined;
    error.value = undefined;
    isInit.value = true;
  };

  /**
   * 执行请求
   */
  const execute = async (...args: Parameters<typeof request>) => {
    try {
      status.value = 'pending';
      error.value = undefined;
      if (clearBeforeRequest) {
        data.value = undefined;
      }
      const result = await request(...args);
      data.value = result;
      status.value = 'success';
      return result;
    } catch (err) {
      error.value = err;
      data.value = undefined;
      status.value = 'error';
    } finally {
      isInit.value = false;
    }
    return null;
  };

  return {
    status,
    data,
    error,

    execute,
    reset,

    isInit,
    isIdle,
    isLoading,
    isSuccess,
    isError,
  };
}
