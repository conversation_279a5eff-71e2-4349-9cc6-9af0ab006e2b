import { computed } from 'vue';

import { useRequest } from '../use-request';

export type Request<R = any> = (...args: any[]) => Promise<R>;
export type Pagination = {
  total: number;
  pageIndex: number;
  pageSize: number;
};
export type SearchRequestOption = {
  clearBeforeRequest?: boolean; // 请求前是否清空数据
  pageIndex?: number;
  pageSize?: number;
};

export const useSearchRequest = <R = any>(request: Request<R>, option?: SearchRequestOption) => {
  const { clearBeforeRequest = true, pageIndex = 1, pageSize = 10 } = option ?? {};

  const use = useRequest(request, { clearBeforeRequest });

  const pagination = computed(() => {
    const dataValue = use.data.value as Partial<Pagination>;
    return {
      total: dataValue?.total ?? 0,
      pageIndex: dataValue?.pageIndex ?? pageIndex,
      pageSize: dataValue?.pageSize ?? pageSize,
    };
  });

  return {
    ...use,
    pagination,
  };
};
