import type { FieldValue, ValidateCallback, WrappedFormUtils } from 'ant-design-vue/types/form/form';
import { ref, type Ref } from 'vue';

export const useFormRef = () => {
  const formRef = ref<WrappedFormUtils>();

  /**
   * 表单验证
   */
  const validateFields = (validator: ValidateCallback) => {
    formRef.value?.validateFields(validator);
  };

  const validateFieldsAndScroll = (validator: ValidateCallback) => {
    formRef.value?.validateFieldsAndScroll(validator);
  };

  /**
   * 表单重置字段
   */
  const resetFields = (names?: string[]) => {
    formRef.value?.resetFields(names);
  };

  /**
   * 表单更新字段值
   */
  const updateFormDataValue = (
    formData: Ref<Record<string, unknown>>,
    data: { name: string; dirty: boolean; touched: boolean; value: unknown }
  ) => {
    Object.keys(data).forEach((key) => {
      if (data[key]) {
        Object.assign(formData.value, {
          [key]: data[key].value,
        });
      }
    });
  };

  /**
   * 设置表单字段值
   */
  const setFieldsValue = (fieldValue: FieldValue) => {
    formRef.value?.setFieldsValue(fieldValue);
  };

  return {
    formRef,
    validateFields,
    setFieldsValue,
    resetFields,
    validateFieldsAndScroll,
    updateFormDataValue,
  };
};
