# use-form-ref

```typescript
const { formRef, validateFields, resetFields, setFieldsValue } = useFormRef();

setup() {
  const formValidator = (errors: Error[], values: Record<string, any>) => {
    // 表单验证逻辑...
  }
  const onSubmit = () => {
    validateFields(formValidator)
  }
  return { onSubmit };
},
render() {
  return (
    <div>
      <MyForm ref={formRef} onSubmit={this.onSubmit} />
    </div>
  )
}
```

```typescript
// MyForm
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

const MyForm = defineComponent({
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
      required: false,
    },
  },
  emits: ['submit'],
  render() {
    return (
      <Form form={this.form} onSubmit={this.onSubmit}>
        <Form.Item label="分组名称">
          <Input
            placeholder="请输入分组名称"
            v-decorator={['groupName', { rules: [{ required: true, message: '分组名称为必填项' }] }]}
          />
        </Form.Item>
      </Form>
    )
  }
})

export default Form.create({}, MyForm);

```
