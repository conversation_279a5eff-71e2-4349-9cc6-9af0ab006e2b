<template>
  <a-breadcrumb class="breadcrumb">
    <a-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="index">
      <router-link :to="item.path" v-if="item.path && index < breadcrumbItems.length - 1">
        {{ item.title }}
      </router-link>
      <span v-else>{{ item.title }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script lang="ts">
import Vue from 'vue';
import { Route } from 'vue-router';

interface BreadcrumbItem {
  title: string;
  path: string;
}

export default Vue.extend({
  name: 'Breadcrumb',
  data() {
    return {
      breadcrumbItems: [] as BreadcrumbItem[],
    };
  },
  methods: {
    // 根据路由生成面包屑
    generateBreadcrumb(route: Route): BreadcrumbItem[] {
      const items: BreadcrumbItem[] = [];

      // 添加首页
      items.push({
        title: '首页',
        path: '/',
      });

      // 根据当前路由匹配的路径生成面包屑
      if (route.name && route.meta?.title) {
        items.push({
          title: route.meta.title as string,
          path: route.path,
        });
      }

      // 如果有额外的路径参数，添加到面包屑
      if (route.params.id) {
        const detailTitle = `详情 ${route.params.id}`;
        items.push({
          title: detailTitle,
          path: '',
        });
      }

      return items;
    },
  },
  watch: {
    $route: {
      handler(route: Route) {
        this.breadcrumbItems = this.generateBreadcrumb(route);
      },
      immediate: true,
    },
  },
});
</script>

<style lang="less" scoped>
.breadcrumb {
  margin-left: 16px;
}
</style>
