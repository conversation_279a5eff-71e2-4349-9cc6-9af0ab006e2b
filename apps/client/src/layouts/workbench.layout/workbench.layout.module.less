.container {
  display: flex;

  .aside {
    width: 180px;
    border-right: 1px solid #eee;
    background: #fff;
  }

  .main {
    flex: 1;
    padding: 10px;
    overflow: auto;
  }

  .nav {
    padding: 10px;

    .heading {
      padding: 5px 5px;
      font-size: 14px;
      font-weight: 700;
      margin-bottom: 5px;
      color: #888;
    }

    .item {
      min-height: 36px;
      font-size: 14px;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px;
      font-weight: 700;
      color: #666;

      &:hover {
        background-color: #e2f1fd;
        border-radius: 4px;
      }
    }

    :global {
      .router-link-active {
        color: #128bed;
        background-color: #e2f1fd;
        border-radius: 4px;
      }
    }
  }
}
