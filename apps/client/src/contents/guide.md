# 研发团队绩效考核体系

## 一、角色考核指标

### 1. 开发工程师指标

#### 1.1 基础效能指标（权重：80%）

| 指标项   | 具体指标                                 | 目标值                      | 数据来源          |
| -------- | ---------------------------------------- | --------------------------- | ----------------- |
| 交付质量 | 个人[总体事故率](#1.2-重大事故率统计示例) | ≤ 2.5%                      | 所有生产 bug      |
|          | 单元测试覆盖率环比                       | 季度提升 ≥3%                | SonarQube         |
|          | 个人[缺陷引入率](#2.2-缺陷指标定义)       | ≤2 缺陷/千行代码(修改+新增) | Teambition+GitLab |
| 交付速度 | 迭代任务完成数                           | 稳定上升                    | Teambition        |

#### 1.2 技术成长与协作指标（权重：20%）

| 指标项             | 具体指标 | 目标值 | 说明                                                              |
| ------------------ | -------- | ------ | ----------------------------------------------------------------- |
| 知识分享及方案建议 | 季度得分 | ≥2 分  | 产品/技术建议/知识分享/文档贡献 [规则详情](#4.-知识分享及方案建议) |

### 2. 技术骨干指标

#### 2.1 技术领导力（权重：60%）

| 指标项             | 具体指标                                  | 目标值 |
| ------------------ | ----------------------------------------- | ------ |
| 知识分享及方案建议 | 季度得分[规则详情](#4.-知识分享及方案建议) | ≥5 分  |
| 新人指导           | 确保新人达到[标准](#5.-新人培养)           | 达标   |

#### 2.2 业务赋能（权重：40%）

| 指标项   | 具体指标   | 目标值 | 计算公式                      |
| -------- | ---------- | ------ | ----------------------------- |
| 需求质量 | 需求缺陷率 | ≤5%    | 缺陷需求数/评审需求总数 ×100% |

### 3. Scrum Master/Leader 指标

#### 3.1 项目效能（权重：70%）

| 指标项       | 具体指标                          | 目标值        | 说明                     |
| ------------ | --------------------------------- | ------------- | ------------------------ |
| 团队交付质量 | 团队[总体事故率](#1.-生产事故管理) | ≤ 2.0%        | 团队内产生的所以生产事故 |
|              | 团队[重大事故率](#1.-生产事故管理) | ≤ 1%          | 团队内 P0/P1 事故率      |
|              | 团队测试覆盖率环比                | 季度提升 ≥3%  | 团队整体代码质量提升     |
|              | 团队[缺陷密度](#2.-缺陷管理)环比   | 季度下降 ≥10% | 团队整体缺陷密度改善     |

| 团队交付效率 | 迭代准时交付率 | ≥90% | 计划内完成任务比例 |

#### 3.2 团队管理（权重：30%）

前后端 leader 负责确保各自团队成员各项指标的达成情况
| 指标项 | 具体指标 | 目标值 | 说明 |
| ------------ | --------------------------------------------------- | ------------------ | ---------------------------- |
| 数据指标跟踪 | 团队成员指标数据收集及时性 | 100% | |
| | [生产事故](#1.-生产事故管理)跟踪率 | 100% | 包括数据搜集记录、处理和复盘 |
| 技术优化 | [技术债务](#3.-技术债务管理)清理率 | 季度提升 ≥15% | |
| | 推进[知识分享及方案建议](#4.-知识分享及方案建议)落地 | 确保团队成员完成率 | |

以上指标相关数据的收集方式[参考](#6.3.1-基础数据搜集)

## 二、实施与优化机制

### 1. 渐进式落地

1. 试点期（1-2 季度）

### 2. 配套激励机制

| 奖项       | 说明           | 激励方式                     |
| ---------- | -------------- | ---------------------------- |
| 技术先锋奖 | 奖励技术创新   | 根据创新方案实际收益追加奖励 |
| 交付护航奖 | 奖励零事故交付 | 季度评选                     |

### 3. 动态校准机制

- 技术委员会季度校准会
- 根据业务变化调整指标基准线
- 收集团队反馈持续优化

## 三、附录：关键定义

### 1. 生产事故管理

#### 1.1 事故分级与修复标准

| 等级 | 定义特征                                | 影响范围            | 初步响应时限 | 临时处置时限 | 彻底修复时限 | 升级机制                        | 典型模块案例                                 |
| ---- | --------------------------------------- | ------------------- | ------------ | ------------ | ------------ | ------------------------------- | -------------------------------------------- |
| P0   | 导致业务中断 ≥30 分钟或经济损失 ≥5 万元 | 全系统/核心模块失效 | ≤30 分钟     | ≤2 小时      | ≤1 工作日    | 超 2 小时未解决需技术总监介入   | 1. 实时风控引擎崩溃<br>2. 数据源接入全量中断 |
| P1   | 核心功能部分失效，影响关键业务流程      | 主要模块功能异常    | ≤1 小时      | ≤4 小时      | ≤3 工作日    | 超 8 小时未解决需团队负责人协调 | 1. 风险评分模型结果错误<br>2. 日报生成失败   |
| P2   | 次核心功能异常，可能引发连锁反应        | 辅助模块功能异常    | ≤2 小时      | ≤1 工作日    | ≤2 周        | 超 24 小时需周会通报            | 1. 预警通知延迟<br>2. 数据可视化异常         |
| P3   | 非核心功能异常，轻微影响使用体验        | 单一功能点异常      | ≤4 小时      | ≤2 工作日    | 按版本计划   | 无强制升级                      | 1. 管理后台操作卡顿<br>2. 日志查询超时       |
| P4   | 辅助功能异常，不影响主流程              | 边缘功能异常        | ≤1 工作日    | 无需紧急处置 | 下个大版本   | 无                              | 1. 导出格式错乱<br>2. 页面样式错位           |

#### 1.2 重大事故率统计示例

| 统计周期 | 事故总数 | 迭代任务数 | P0+P1 事故数 | 传统重大事故率 | 优化重大事故率 |
| -------- | -------- | ---------- | ------------ | -------------- | -------------- |
| 2025Q1   | 120      | 800        | 4            | 3.3% (4/120)   | 0.5% (4/800)   |
| 2025Q2   | 90       | 850        | 3            | 2.2% (2/90)    | 0.24% (2/850)  |

_说明：_

- 传统重大事故率 = (P0+P1 事故数) / 事故总数 ×100%
- 优化重大事故率 = (P0+P1 事故数) / 迭代任务数 ×100%

#### 1.3 事故相关考核指标

##### 团队级指标

1. 重大事故率 ≤3.0%（当前季度）
2. 事故复盘完成率 100%
3. 核心模块单元测试覆盖率 ≥60%

##### 个人级指标

1. 缺陷引入率 ≤1.2 缺陷/KLOC
2. 事故响应及时率 ≥95%
3. 技术文档贡献 ≥2 篇/季度

### 2. 缺陷管理

#### 2.1 缺陷类型分类标准 ​

| 缺陷来源     | 典型工具            | 缺陷特征                                                             | 生命周期阶段 | 有效性验证周期 |
| ------------ | ------------------- | -------------------------------------------------------------------- | ------------ | -------------- |
| 静态代码缺陷 | SonarQube/Checkmarx | 代码规范违反、安全漏洞（如 SQL 注入）、潜在空指针、内存泄漏等        | 编码阶段     | 实时扫描       |
| 动态测试缺陷 | Jira/TestRail       | 功能逻辑错误（如计算错误）、接口异常（参数不匹配）、性能不达标       | 测试阶段     | 迭代周期内修复 |
| 生产缺陷     | ELK/New Relic       | 运行时崩溃（如内存溢出）、性能瓶颈（高延迟）、数据丢失、安全漏洞利用 | 运维阶段     | 持续监控       |

#### 2.2 缺陷指标定义

| 指标名称   | 计算公式                                    | 核心焦点     | 优化目标       | 统计周期                 | 数据来源                      | 典型阈值参考      |
| ---------- | ------------------------------------------- | ------------ | -------------- | ------------------------ | ----------------------------- | ----------------- |
| 缺陷引入率 | `(引入缺陷数 / (新增+修改代码行数)) × 1000` | 每次代码提交 | 新代码质量     | 控制开发过程中的缺陷产生 | 版本控制系统（如 Git） + Jira | ≤1.5‰（CMMI3 级） |
| 缺陷密度 1 | `(总体缺陷数 / 维护代码行数) × 1000`        | 季度/年度    | 历史代码健康度 | 降低技术债务累积风险     | SonarQube + 生产监控          | ≤0.8‰（金融行业） |
| 缺陷密度 2 | `总体缺陷数 / 总的任务数`                   | 季度/年度    | 历史代码健康度 | 降低技术债务累积风险     | SonarQube + 生产监控          | ≤0.8‰（金融行业） |

#### 2.3 有效代码统计方法

采用 `cloc` 工具进行有效代码统计，排除测试目录及特定配置文件：

```bash
cloc --exclude-dir=tests,venv --not-match-f="config.yml" src/
```

输出示例：

```bash
Language      files     blank   comment      code
Python          15       280       156      1920
```

#### 2.4 开发者贡献统计

通过 Git 命令获取开发者的代码归属：

```bash
#!/bin/bash

# 获取项目名称（优先使用CI环境变量，否则动态解析）
PROJECT_NAME=${CI_PROJECT_NAME:-$(basename $(git rev-parse --show-toplevel))}

# 生成Git日志统计
git log --no-merges --all --since="1 year ago" --date=iso --pretty=format:"%ae|%ad" --numstat -- '*.js' '*.ts' '*.java' '*.tsx' '*.css' '*.scss' '*.less' '*.vue' ':!**/__snapshots__/**' ':!*.spec.*' |
awk -v project="$PROJECT_NAME" '
BEGIN {
    OFS=",";
    print "项目名称,开发者邮箱,日期范围,新增行数,删除行数,修改行数";
}
# 处理提交头
/^[^|]+@[^|]+\|[0-9]{4}-[0-9]{2}-[0-9]{2}/ {
    split($0, arr, "|");
    split(arr[1], parts, "@");  # 新增分割操作[1,6](@ref)
    email = parts[1];            # 只保留@前的用户名[1,6](@ref)
    split(arr[2], dt, /[- :]/);
    commit_month = sprintf("%04d-%02d", dt[1], dt[2]);
    next;
}
# 处理文件变更行
$3 ~ /\.(js|ts|java|less|tsx|css|scss|vue)$/i && $3 !~ /(__snapshots__|\.spec)/ {
    split($0, arr, "\t");
    add[email,commit_month] += arr[1];
    del[email,commit_month] += arr[2];
    update[email,commit_month] += (arr[1]>0 && arr[2]>0 ? (arr[1]<arr[2] ? arr[1] : arr[2]) : 0);
    next;
}
END {
    for (key in add) {
        split(key, sep, SUBSEP);
        email = sep[1];
        month = sep[2];
        if (add[key] > 0 || del[key] > 0) {
            printf "%s,%s,%s,%d,%d,%d\n", 
                project, 
                email, 
                month, 
                add[key], 
                del[key], 
                update[key]
        }
    }
}' |
sort -t ',' -k3,3n -k2,2

```
输出：  
```bash
项目名称,开发者邮箱,日期范围,新增行数,删除行数,修改行数
dd-platform-service,<EMAIL>,2024-12,149,28,28
dd-platform-service,<EMAIL>,2024-12,158003,1,1
dd-platform-service,<EMAIL>,2024-12,158001,15,15
dd-platform-service,<EMAIL>,2024-12,158527,208,207
dd-platform-service,<EMAIL>,2024-12,157390,0,0
dd-platform-service,<EMAIL>,2024-12,157536,137,127
dd-platform-service,<EMAIL>,2025-02,161558,23,18
dd-platform-service,<EMAIL>,2025-04,191,4,4
dd-platform-service,<EMAIL>,2025-03,165273,0,0
dd-platform-service,<EMAIL>,2025-04,664582,0,0
dd-platform-service,<EMAIL>,2025-01,152064,265,148
dd-platform-service,<EMAIL>,2025-02,783888,568,504
dd-platform-service,<EMAIL>,2025-03,1081662,48657,41248
dd-platform-service,<EMAIL>,2025-04,1352721,3500,2805
dd-platform-service,<EMAIL>,2025-05,5352,281,255
dd-platform-service,<EMAIL>,2025-02,1002,671,445
dd-platform-service,<EMAIL>,2025-03,173660,5878,5331
dd-platform-service,<EMAIL>,2025-04,239080,3421,2477
dd-platform-service,<EMAIL>,2025-04,448982,51,51
dd-platform-service,<EMAIL>,2025-01,303643,5,5
dd-platform-service,<EMAIL>,2025-02,464394,235,223
```

考核数据表示例：

| 开发者 | 有效代码量 (KLOC) | 缺陷数 | 缺陷密度 |
| ------ | ----------------- | ------ | -------- |
| 张三   | 12.5              | 5      | 0.40     |
| 李四   | 8.7               | 7      | 0.80     |

### 3. 技术债务管理

#### 3.1 技术债务分类

| 类型     | 具体表现示例                 | 严重等级 |
| -------- | ---------------------------- | -------- |
| 代码债务 | 重复代码/过长方法            | P1-P3    |
| 架构债务 | 不合理分层/单点故障/过度耦合 | P1-P2    |
| 测试债务 | 无单元测试/过时测试用例      | P2-P3    |
| 文档债务 | 接口文档缺失/过时设计文档    | P3       |

#### 3.2 严重等级标准

- P1 关键债务：影响核心流程，必须当季解决
- P2 重要债务：影响扩展性，3 个月内解决
- P3 普通债务：优化项，可跨季度解决

#### 3.3 技术债务清理率计算

##### 3.3.1 基础清理率

基础清理率 = (当期已解决技术债务项 ÷ 期初技术债务项) × 100%

##### 3.3.2 加权清理率

加权清理率 = (Σ(解决债务权重) ÷ Σ(全部债务权重)) × 100%

**权重系数**：

- P1 级债务 = 1.5
- P2 级债务 = 1.0
- P3 级债务 = 0.5

##### 3.3.3 计算示例

| 债务等级 | 期初数量 | 解决数量 |
| -------- | -------- | -------- |
| P1       | 5        | 3        |
| P2       | 10       | 6        |
| P3       | 15       | 4        |

**计算过程**：

1. 基础清理率 = (3+6+4)/(5+10+15) × 100% = 13/30 × 100% ≈ 43.33%
2. 加权清理率 = (3×1.5 + 6×1.0 + 4×0.5)/(5×1.5 + 10×1.0 + 15×0.5) × 100%
   = (4.5 + 6 + 2)/(7.5 + 10 + 7.5) × 100%
   = 12.5/25 × 100% = 50%

#### 3.4 技术债务管理实施策略(暂)

##### 3.4.1 分阶段目标

| 阶段   | 时间跨度 | 基础清理率目标 | 重点关注领域           |
| ------ | -------- | -------------- | ---------------------- |
| 起步期 | 1-2 季度 | ≥30%           | 代码规范、文档完善     |
| 提升期 | 3-4 季度 | ≥40%           | 单元测试覆盖、架构优化 |
| 稳定期 | 5 季度后 | ≥50%           | 持续优化、预防为主     |

##### 3.4.2 团队协作机制

1. 每个迭代指定 1-2 个技术债务专项任务
2. 建立技术债务评审机制
   - 双周评审一次
   - 重点关注新增技术债务
   - 评估现有债务处理进度

##### 3.4.3 激励措施

1. 设立技术债务清理专项奖金
2. 将技术债务处理情况纳入晋升考核
3. 优秀实践案例在团队内分享

### 4. 知识分享及方案建议

#### 4.1 知识分享

```
├── 01-技术规范    # API规范/数据库设计等
├── 02-解决方案    # 典型业务场景实现方案
├── 03-技术研究    # 新技术调研报告
└── 04-最佳实践    # 性能优化/问题排查案例/CodeReview
└── 05-架构分享    # 本团队或者跨团队架构解析
```

#### 4.2 分享/方案建议质量要求

1. 更新及时性：文档必须与代码/方案同步更新
2. 完整性：包含必要的背景、方案、结论
3. 可追溯性：关联相关需求、代码、评审记录
4. 如果是建议方案，需要有以下要求
   - 提案类型：架构优化/流程改进/技术创新
   - 预期影响：高/中/低
   - 关联需求：JIRA-XXX
   - 评审人：@架构师 @产品经理

#### 4.3 评分标准

| 季度提案数量 | 计分公式                    | 示例                                    | 设计理念       |
| ------------ | --------------------------- | --------------------------------------- | -------------- |
| ≤3 个        | 采纳数 ×2 + 未采纳数 ×0.5   | 2 个提案中 1 个采纳：2×1+1×0.5=2.5 分   | 鼓励高质量     |
| 4-6 个       | 采纳数 ×1.5 + 未采纳数 ×0.3 | 5 个提案中 3 个采纳：3×1.5+2×0.3=5.1 分 | 平衡质量与数量 |
| ≥7 个        | 采纳数 ×1 + 未采纳数 ×0.2   | 8 个提案中 4 个采纳：4×1+4×0.2=4.8 分   | 抑制低质量提案 |

### 5. 新人培养

具体值需要重新定义
| 考核维度 | 达标线 | 优秀线 |
|----------------|----------------------|----------------------|
| 新人产出效率 | 达到团队均值 60% | 达到团队均值 85% |
| 代码质量 | 缺陷密度 ≤2.0/KLOC | 缺陷密度 ≤1.2/KLOC |
| 培养周期 | ≤8 周 | ≤6 周 |

### 6. 数据采集与度量

#### 6.1 自动化工具

| 工具                   | 用途         | 采集指标                   |
| ---------------------- | ------------ | -------------------------- |
| SonarQube+GitLab CI/CD | 代码质量度量 | 测试覆盖率、代码规范符合度 |
| Teambition             | 任务追踪     | 任务完成率、工时统计       |
| ELK                    | 效能分析     | 生产事故、性能指标         |

#### 6.2 数据收集要求

1. 自动化优先：优先使用工具自动采集
2. 及时性：手动数据当天录入
3. 准确性：数据需要有证据支持
4. 可追溯：保留原始数据来源

#### 6.3 数据采集分析

##### 6.3.1 基础数据搜集

1. Teambition 中对应的任务看板有以下动作，通过 webhook 通知到后端
   1. 创建 bug，关闭 bug ，这类数据标记为 data_bug
   2. 创建任务，分配任务, 标记为 data_task
   3. 创建迭代
   4. 加入任务到迭代，从迭代中移除任务
   5. 任务增加标签 **需求评审有误** , 标记为 data_evaluation_error
   6. 任务上增加字段 **需求评审人**，该字段值发生变化的时候 , 标记/变更 data_evaluation_task
   7. 任务增加标签 **技术债任务** , 标记为 data_technical_debt
   8. 任务增加标签 **延迟完成** data_task_delayed
2. 集成 sonar-lint 插件，提交前针对当前提交的代码做代码分析并提醒
3. gitlab 提交后自动触发 sonnar-scanner 提交最新的分析到 sonarqube
4. 增加线上问题根据记录，具体内容[参考](#1.1-事故分级与修复标准)
5. 知识分享及方案建议 通过 部门 Teambition 项目 来跟进

##### 6.3.2 数据分析流程

所有分析都需要先选定分析周期，迭代？月？季度？半年度？年度？

1. 个人缺陷引入率
   1. 从 gitlab 上获取新提交的代码量(新增+修改)
   2. 从 sonarqube 上获取引入的 bug 数量 bug1
   3. 从记录的 [Teambition 记录](#6.3.1-基础数据搜集) 获取到的 bug 的数量 bug2
   4. 缺陷引入率 = ((bug1+bug2) / (新增+修改代码行数)) × 1000
2. 团队缺陷密度
   1. 选择项目
   2. 从 sonarqube 上获取遗留的 bug 数量 bug1
   3. 从记录的 [Teambition 记录](#6.3.1-基础数据搜集) 获取到的 bug 的数量 bug2
   4. cloc 工具统计选定项目所有代码行数
   5. 缺陷密度 = (bug1+bug2) / 维护代码行数) × 1000
3. 生产事故率/复盘
   1. 选定项目
   2. 从[生产问题记录](#6.3.1-基础数据搜集)获取生成事故数量
   3. 从 [Teambition 记录](#6.3.1-基础数据搜集) 获取期间所有任务
   4. 生产事故率 = 生成事故数量 / 任务总数
   5. 复盘之后记录会议纪要并添加到对应的事故上
4. 技术债务清理率
   1. 选定项目
   2. 从 [Teambition 记录](#6.3.1-基础数据搜集) 上获取遗留技术债及已经修复的
   3. 计算技术债务清理率
5. 单元测试覆盖率提升
   1. 根据选定的分析周期，获取每个周期的单元测试率的变化
   2. 从 Sonarqube 上获取单元测试覆盖率
6. 需求缺陷率
   1. 选定项目
   2. 从 [Teambition 记录](#6.3.1-基础数据搜集) 获取参与需求评审的任务 quantity1 以及出问题的需求 quantity2
7. 迭代任务完成数
   1. 选定项目
   2. 从 [Teambition 记录](#6.3.1-基础数据搜集) 获取每个迭代完成的任务数
8. 迭代完成率
   1. 选定项目
   2. 从 [Teambition 记录](#6.3.1-基础数据搜集) 获取每个迭代完成的任务数以及迭代初期规划的任务数
9. 知识分享及方案建议
   1. 通过 部门 Teambition 项目 搜集计算
