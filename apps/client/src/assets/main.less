/* 全局样式 */
html,
body {
  margin: 0;
  padding: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
}

/* 清除浮动 */
.clearfix:after {
  content: '';
  display: table;
  clear: both;
}

/* 通用卡片样式 */
.card-container {
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 通用表格样式 */
.table-container {
  background-color: #fff;
  padding: 16px;
  border-radius: 2px;
}

/* 通用表单样式 */
.form-container {
  max-width: 800px;
  padding: 24px;
  background-color: #fff;
  border-radius: 2px;
}

/* 通用页面容器样式 */
.page-container {
  padding: 24px;
}

/* 通用操作按钮组样式 */
.action-buttons {
  margin-bottom: 16px;

  .ant-btn {
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}

/* 通用搜索表单样式 */
.search-form {
  margin-bottom: 16px;
  padding: 24px 24px 0;
  background-color: #fff;
  border-radius: 2px;
}

/* 通用详情页样式 */
.detail-container {
  background-color: #fff;
  padding: 24px;
  border-radius: 2px;

  .detail-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .detail-title {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .detail-meta {
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .detail-content {
    margin-bottom: 24px;
  }

  .detail-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

/* 响应式工具类 */
.hidden-xs {
  @media (max-width: 575px) {
    display: none !important;
  }
}

.hidden-sm {
  @media (max-width: 767px) {
    display: none !important;
  }
}

.hidden-md {
  @media (max-width: 991px) {
    display: none !important;
  }
}

.hidden-lg {
  @media (max-width: 1199px) {
    display: none !important;
  }
}

/* 文本工具类 */
.text-primary {
  color: #1890ff;
}

.text-success {
  color: #52c41a;
}

.text-warning {
  color: #faad14;
}

.text-danger {
  color: #f5222d;
}

.text-muted {
  color: rgba(0, 0, 0, 0.45);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

/* 边距工具类 */
.mt-0 {
  margin-top: 0 !important;
}
.mt-1 {
  margin-top: 4px !important;
}
.mt-2 {
  margin-top: 8px !important;
}
.mt-3 {
  margin-top: 16px !important;
}
.mt-4 {
  margin-top: 24px !important;
}
.mt-5 {
  margin-top: 32px !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}
.mb-1 {
  margin-bottom: 4px !important;
}
.mb-2 {
  margin-bottom: 8px !important;
}
.mb-3 {
  margin-bottom: 16px !important;
}
.mb-4 {
  margin-bottom: 24px !important;
}
.mb-5 {
  margin-bottom: 32px !important;
}

.ml-0 {
  margin-left: 0 !important;
}
.ml-1 {
  margin-left: 4px !important;
}
.ml-2 {
  margin-left: 8px !important;
}
.ml-3 {
  margin-left: 16px !important;
}
.ml-4 {
  margin-left: 24px !important;
}
.ml-5 {
  margin-left: 32px !important;
}

.mr-0 {
  margin-right: 0 !important;
}
.mr-1 {
  margin-right: 4px !important;
}
.mr-2 {
  margin-right: 8px !important;
}
.mr-3 {
  margin-right: 16px !important;
}
.mr-4 {
  margin-right: 24px !important;
}
.mr-5 {
  margin-right: 32px !important;
}

.p-0 {
  padding: 0 !important;
}
.p-1 {
  padding: 4px !important;
}
.p-2 {
  padding: 8px !important;
}
.p-3 {
  padding: 16px !important;
}
.p-4 {
  padding: 24px !important;
}
.p-5 {
  padding: 32px !important;
}

/* 弹性布局工具类 */
.d-flex {
  display: flex !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

/* 阴影工具类 */
.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
}

/* 边框工具类 */
.border {
  border: 1px solid #f0f0f0 !important;
}

.border-top {
  border-top: 1px solid #f0f0f0 !important;
}

.border-right {
  border-right: 1px solid #f0f0f0 !important;
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0 !important;
}

.border-left {
  border-left: 1px solid #f0f0f0 !important;
}

.rounded {
  border-radius: 2px !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

/* 宽度和高度工具类 */
.w-100 {
  width: 100% !important;
}

.h-100 {
  height: 100% !important;
}

/* 溢出工具类 */
.overflow-hidden {
  overflow: hidden !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

/* 位置工具类 */
.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

/* 显示工具类 */
.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

/* 背景工具类 */
.bg-white {
  background-color: #fff !important;
}

.bg-primary {
  background-color: #1890ff !important;
}

.bg-success {
  background-color: #52c41a !important;
}

.bg-warning {
  background-color: #faad14 !important;
}

.bg-danger {
  background-color: #f5222d !important;
}

.bg-light {
  background-color: #f5f5f5 !important;
}

.bg-dark {
  background-color: #262626 !important;
}
