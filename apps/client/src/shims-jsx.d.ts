import Vue, { VNode, CreateElement } from 'vue';

declare global {
  namespace JSX {
    interface Element extends VNode {}
    interface ElementClass extends Vue {}
    interface IntrinsicElements {
      [elem: string]: any;
    }
    interface ElementAttributesProperty {
      $props: any;
    }
  }
}

declare module 'vue' {
  interface ComponentOptionsBase<V, D, C, M, E> {
    render?(createElement: CreateElement): VNode;
  }
}

declare const React: any;
