import { defineStore } from 'pinia';

import { userService } from '@/core/services';

interface UserState {
  currentUser: any | null;
  loading: boolean;
  error: string | null;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    currentUser: null,
    loading: false,
    error: null,
  }),

  getters: {
    isLoggedIn: (state) => !!state.currentUser,
    isAdmin: (state) => state.currentUser?.role === 'ADMIN',
  },

  actions: {
    async login(username: string, password: string) {
      this.loading = true;
      this.error = null;

      try {
        console.log('userStore.login 被调用:', { username, password });
        // 修正：确保传递的是正确的对象格式
        const response = await userService.login({ username, password });
        console.log('登录响应:', response);

        // 后端返回的格式是 {user: {...}}，需要提取 user 字段
        const user = response.user || response;
        this.currentUser = user;
        localStorage.setItem('user', JSON.stringify(user));
        return user;
      } catch (error: any) {
        console.error('登录错误详情:', error);
        this.error = error.message || '登录失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async logout() {
      this.loading = true;

      try {
        await userService.logout();
        this.currentUser = null;
        localStorage.removeItem('user');
      } catch (error: any) {
        this.error = error.message || '登出失败';
      } finally {
        this.loading = false;
      }
    },

    async fetchCurrentUser() {
      this.loading = true;

      try {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          this.currentUser = JSON.parse(storedUser);
        } else {
          const response = await userService.getProfile();
          this.currentUser = response;
          localStorage.setItem('user', JSON.stringify(response));
        }
      } catch (error: any) {
        this.error = error.message || '获取用户信息失败';
        this.currentUser = null;
        localStorage.removeItem('user');
      } finally {
        this.loading = false;
      }
    },
  },
});
