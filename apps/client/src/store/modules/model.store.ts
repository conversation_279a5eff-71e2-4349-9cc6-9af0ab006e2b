import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
import axios from 'axios';

interface Model {
  id: string;
  name: string;
  description: string;
  modelType: string;
  version: string;
  trainingJobId: string;
  datasetId: string;
  metrics: Record<string, number>;
  parameters: Record<string, any>;
  filePath: string;
  createdAt: Date;
  createdBy: string;
  status: string;
  approvedBy: string | null;
  approvedAt: Date | null;
}

interface ModelState {
  models: Model[];
  currentModel: Model | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: Record<string, any>;
}

export const useModelStore = defineStore('model', {
  state: (): ModelState => ({
    models: [],
    currentModel: null,
    loading: false,
    error: null,
    totalCount: 0,
    currentPage: 1,
    pageSize: 10,
    filters: {},
  }),

  getters: {
    getModelById: (state) => (id: string) => {
      return state.models.find((model) => model.id === id);
    },
  },

  actions: {
    async fetchModels(page = 1, pageSize = 10, filters = {}) {
      this.loading = true;
      this.error = null;
      this.currentPage = page;
      this.pageSize = pageSize;
      this.filters = filters;

      try {
        const response = await axios.get('/api/models', {
          params: {
            page,
            pageSize,
            ...filters,
          },
        });

        this.models = response.data.items;
        this.totalCount = response.data.totalCount;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取模型列表失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchModelById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/models/${id}`);
        this.currentModel = response.data;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取模型详情失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async registerModel(data: Partial<Model>) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post('/api/models', data);
        message.success('模型注册成功');
        return response.data;
      } catch (error: any) {
        this.error = error.message || '注册模型失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async approveModel(id: string, approvalData: { comment?: string }) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post(`/api/models/${id}/approve`, approvalData);
        message.success('模型已批准');

        // 更新本地数据
        if (this.currentModel && this.currentModel.id === id) {
          this.currentModel = { ...this.currentModel, ...response.data };
        }

        const index = this.models.findIndex((model) => model.id === id);
        if (index !== -1) {
          this.models[index] = { ...this.models[index], ...response.data };
        }

        return response.data;
      } catch (error: any) {
        this.error = error.message || '批准模型失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async compareModels(ids: string[]) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post('/api/models/compare', { ids });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '比较模型失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async downloadModel(id: string, format: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/models/${id}/download`, {
          params: { format },
          responseType: 'blob',
        });

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `model-${id}.${format}`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return response.data;
      } catch (error: any) {
        this.error = error.message || '下载模型失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteModel(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await axios.delete(`/api/models/${id}`);
        message.success('模型删除成功');

        // 更新本地数据
        if (this.currentModel && this.currentModel.id === id) {
          this.currentModel = null;
        }

        this.models = this.models.filter((model) => model.id !== id);

        return true;
      } catch (error: any) {
        this.error = error.message || '删除模型失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getModelTypeMetrics(modelType: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/models/metrics-definitions`, {
          params: { modelType },
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取模型指标定义失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
