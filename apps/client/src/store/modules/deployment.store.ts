import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
import axios from 'axios';

interface Deployment {
  id: string;
  name: string;
  description: string;
  modelId: string;
  modelType: string;
  engineId: string;
  endpoint: string;
  version: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  metrics: Record<string, number>;
}

interface DeploymentState {
  deployments: Deployment[];
  currentDeployment: Deployment | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: Record<string, any>;
}

export const useDeploymentStore = defineStore('deployment', {
  state: (): DeploymentState => ({
    deployments: [],
    currentDeployment: null,
    loading: false,
    error: null,
    totalCount: 0,
    currentPage: 1,
    pageSize: 10,
    filters: {},
  }),

  getters: {
    getDeploymentById: (state) => (id: string) => {
      return state.deployments.find((deployment) => deployment.id === id);
    },
  },

  actions: {
    async fetchDeployments(page = 1, pageSize = 10, filters = {}) {
      this.loading = true;
      this.error = null;
      this.currentPage = page;
      this.pageSize = pageSize;
      this.filters = filters;

      try {
        const response = await axios.get('/api/deployments', {
          params: {
            page,
            pageSize,
            ...filters,
          },
        });

        this.deployments = response.data.items;
        this.totalCount = response.data.totalCount;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取部署列表失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchDeploymentById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/deployments/${id}`);
        this.currentDeployment = response.data;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取部署详情失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deployModel(modelId: string, config: any) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post('/api/deployments', {
          modelId,
          ...config,
        });
        message.success('模型部署成功');
        return response.data;
      } catch (error: any) {
        this.error = error.message || '部署模型失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateDeployment(id: string, updates: any) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.put(`/api/deployments/${id}`, updates);
        message.success('部署更新成功');

        // 更新本地数据
        if (this.currentDeployment && this.currentDeployment.id === id) {
          this.currentDeployment = { ...this.currentDeployment, ...response.data };
        }

        const index = this.deployments.findIndex((deployment) => deployment.id === id);
        if (index !== -1) {
          this.deployments[index] = { ...this.deployments[index], ...response.data };
        }

        return response.data;
      } catch (error: any) {
        this.error = error.message || '更新部署失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async rollbackDeployment(id: string, version: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post(`/api/deployments/${id}/rollback`, {
          version,
        });
        message.success('部署回滚成功');

        // 更新本地数据
        if (this.currentDeployment && this.currentDeployment.id === id) {
          this.currentDeployment = { ...this.currentDeployment, ...response.data };
        }

        const index = this.deployments.findIndex((deployment) => deployment.id === id);
        if (index !== -1) {
          this.deployments[index] = { ...this.deployments[index], ...response.data };
        }

        return response.data;
      } catch (error: any) {
        this.error = error.message || '回滚部署失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteDeployment(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await axios.delete(`/api/deployments/${id}`);
        message.success('部署删除成功');

        // 更新本地数据
        if (this.currentDeployment && this.currentDeployment.id === id) {
          this.currentDeployment = null;
        }

        this.deployments = this.deployments.filter((deployment) => deployment.id !== id);

        return true;
      } catch (error: any) {
        this.error = error.message || '删除部署失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async testDeployment(id: string, input: Record<string, any>) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post(`/api/deployments/${id}/test`, input);
        return response.data;
      } catch (error: any) {
        this.error = error.message || '测试部署失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getDeploymentEngineConfig(modelType: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/deployment-engines/config`, {
          params: { modelType },
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取部署引擎配置失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
