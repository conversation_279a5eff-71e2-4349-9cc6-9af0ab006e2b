import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
import axios from 'axios';

interface TrainingJob {
  id: string;
  name: string;
  description: string;
  datasetId: string;
  modelType: string;
  engineId: string;
  parameters: Record<string, any>;
  status: string;
  createdAt: Date;
  startedAt: Date | null;
  completedAt: Date | null;
  createdBy: string;
  metrics: Record<string, number>;
  logs: string[];
  modelId: string | null;
}

interface TrainingState {
  trainingJobs: TrainingJob[];
  currentTrainingJob: TrainingJob | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: Record<string, any>;
}

export const useTrainingStore = defineStore('training', {
  state: (): TrainingState => ({
    trainingJobs: [],
    currentTrainingJob: null,
    loading: false,
    error: null,
    totalCount: 0,
    currentPage: 1,
    pageSize: 10,
    filters: {},
  }),

  getters: {
    getTrainingJobById: (state) => (id: string) => {
      return state.trainingJobs.find((job) => job.id === id);
    },
  },

  actions: {
    async fetchTrainingJobs(page = 1, pageSize = 10, filters = {}) {
      this.loading = true;
      this.error = null;
      this.currentPage = page;
      this.pageSize = pageSize;
      this.filters = filters;

      try {
        const response = await axios.get('/api/training-jobs', {
          params: {
            page,
            pageSize,
            ...filters,
          },
        });

        this.trainingJobs = response.data.items;
        this.totalCount = response.data.totalCount;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取训练任务列表失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchTrainingJobById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/training-jobs/${id}`);
        this.currentTrainingJob = response.data;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取训练任务详情失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async createTrainingJob(data: Partial<TrainingJob>) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post('/api/training-jobs', data);
        message.success('训练任务创建成功');
        return response.data;
      } catch (error: any) {
        this.error = error.message || '创建训练任务失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async startTrainingJob(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post(`/api/training-jobs/${id}/start`);
        message.success('训练任务已启动');

        // 更新本地数据
        if (this.currentTrainingJob && this.currentTrainingJob.id === id) {
          this.currentTrainingJob = { ...this.currentTrainingJob, ...response.data };
        }

        const index = this.trainingJobs.findIndex((job) => job.id === id);
        if (index !== -1) {
          this.trainingJobs[index] = { ...this.trainingJobs[index], ...response.data };
        }

        return response.data;
      } catch (error: any) {
        this.error = error.message || '启动训练任务失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async stopTrainingJob(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post(`/api/training-jobs/${id}/stop`);
        message.success('训练任务已停止');

        // 更新本地数据
        if (this.currentTrainingJob && this.currentTrainingJob.id === id) {
          this.currentTrainingJob = { ...this.currentTrainingJob, ...response.data };
        }

        const index = this.trainingJobs.findIndex((job) => job.id === id);
        if (index !== -1) {
          this.trainingJobs[index] = { ...this.trainingJobs[index], ...response.data };
        }

        return response.data;
      } catch (error: any) {
        this.error = error.message || '停止训练任务失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getTrainingLogs(id: string, options = {}) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/training-jobs/${id}/logs`, {
          params: options,
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取训练日志失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getTrainingMetrics(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/training-jobs/${id}/metrics`);
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取训练指标失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getTrainingEngineConfig(modelType: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/training-engines/config`, {
          params: { modelType },
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取训练引擎配置失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
