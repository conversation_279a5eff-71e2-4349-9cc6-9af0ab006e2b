import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
import axios from 'axios';

// 模型类型枚举，与后端保持一致
export enum ModelTypeEnum {
  INNOVATION_HEALTH_MODEL = 'innovation_health_model',
  GENERIC_RISK_MODEL = 'generic_risk_model',
}

// 数据集状态枚举，与后端保持一致
export enum DatasetStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  READY = 'ready',
  ERROR = 'error',
}

interface Dataset {
  id: string;
  name: string;
  description: string;
  modelType: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  fileSize: number;
  rowCount: number;
  columnCount: number;
  schema: any;
  tags: string[];
  status: DatasetStatus;
}

// 数据集筛选参数，与后端DatasetFiltersDto保持一致
interface DatasetFilters {
  modelType?: ModelTypeEnum;
  status?: DatasetStatus;
  createdBy?: string;
  search?: string;
  pageIndex?: number;
  pageSize?: number;
  sortOrder?: 'ASC' | 'DESC';
}

// 分页响应格式，与后端PaginationResponse保持一致
interface PaginationResponse<T> {
  pageIndex: number;
  pageSize: number;
  total: number;
  data: T[];
}

interface DatasetState {
  datasets: Dataset[];
  currentDataset: Dataset | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: DatasetFilters;
}

export const useDatasetStore = defineStore('dataset', {
  state: (): DatasetState => ({
    datasets: [],
    currentDataset: null,
    loading: false,
    error: null,
    totalCount: 0,
    currentPage: 1,
    pageSize: 10,
    filters: {},
  }),

  getters: {
    getDatasetById: (state) => (id: string) => {
      return state.datasets.find((dataset) => dataset.id === id);
    },
  },

  actions: {
    async fetchDatasets(pageIndex = 1, pageSize = 10, filters: Partial<DatasetFilters> = {}) {
      this.loading = true;
      this.error = null;
      this.currentPage = pageIndex;
      this.pageSize = pageSize;
      this.filters = { ...filters, pageIndex, pageSize };

      try {
        // 使用POST方法调用/datasets/search接口
        const response = await axios.post('/api/datasets/search', {
          pageIndex,
          pageSize,
          ...filters,
        });

        const result: PaginationResponse<Dataset> = response.data;
        this.datasets = result.data;
        this.totalCount = result.total;
        return result;
      } catch (error: any) {
        this.error = error.message || '获取数据集列表失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchDatasetById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        // 使用GET方法调用/datasets/detail/:id接口
        const response = await axios.get(`/api/datasets/detail/${id}`);
        this.currentDataset = response.data;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取数据集详情失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async createDataset(data: { name: string; description?: string; modelType: ModelTypeEnum; tags?: string[] }) {
      this.loading = true;
      this.error = null;

      try {
        // 使用POST方法调用/datasets接口
        const response = await axios.post('/api/datasets', data);
        message.success('数据集创建成功');
        return response.data;
      } catch (error: any) {
        this.error = error.message || '创建数据集失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateDataset(
      id: string,
      data: {
        name?: string;
        description?: string;
        tags?: string[];
      }
    ) {
      this.loading = true;
      this.error = null;

      try {
        // 使用POST方法调用/datasets/:id/update接口
        const response = await axios.post(`/api/datasets/${id}/update`, data);
        message.success('数据集更新成功');

        // 更新本地数据
        if (this.currentDataset && this.currentDataset.id === id) {
          this.currentDataset = { ...this.currentDataset, ...response.data };
        }

        const index = this.datasets.findIndex((dataset) => dataset.id === id);
        if (index !== -1) {
          this.datasets[index] = { ...this.datasets[index], ...response.data };
        }

        return response.data;
      } catch (error: any) {
        this.error = error.message || '更新数据集失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteDataset(id: string) {
      this.loading = true;
      this.error = null;

      try {
        // 使用POST方法调用/datasets/:id/delete接口
        await axios.post(`/api/datasets/${id}/delete`);
        message.success('数据集删除成功');

        // 更新本地数据
        if (this.currentDataset && this.currentDataset.id === id) {
          this.currentDataset = null;
        }

        this.datasets = this.datasets.filter((dataset) => dataset.id !== id);
        this.totalCount -= 1;

        return true;
      } catch (error: any) {
        this.error = error.message || '删除数据集失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getDatasetPreview(id: string, pageIndex = 1, pageSize = 100) {
      this.loading = true;
      this.error = null;

      try {
        // 使用POST方法调用/datasets/:id/items接口
        const response = await axios.post(`/api/datasets/${id}/items`, {
          pageIndex,
          pageSize,
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取数据集预览失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async validateDataset(id: string, modelType: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post(`/api/datasets/${id}/validate`, {
          modelType,
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '验证数据集失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getDatasetSchema(modelType: ModelTypeEnum) {
      this.loading = true;
      this.error = null;

      try {
        // 使用GET方法调用/datasets/schema接口
        const response = await axios.get(`/api/datasets/schema`, {
          params: { modelType },
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取数据集模式失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 新增：上传数据集文件
    async uploadDatasetFile(id: string, file: File, badCase?: string) {
      this.loading = true;
      this.error = null;

      try {
        const formData = new FormData();
        formData.append('file', file);
        if (badCase) {
          formData.append('badCase', badCase);
        }

        // 使用POST方法调用/datasets/:id/upload接口
        const response = await axios.post(`/api/datasets/${id}/upload`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        message.success('文件上传成功');
        return response.data;
      } catch (error: any) {
        this.error = error.message || '文件上传失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
