import { defineStore } from 'pinia';

import { userService } from '@/core/services';
import type { UserEntity } from '@/core/entities';

export const useAppStore = defineStore('app', {
  state: () => ({
    users: [] as UserEntity[],
    usersCacheTime: 0,
  }),

  getters: {
    employeeOptions: (state) => {
      return state.users.map((user) => ({
        label: user.username,
        value: user.username,
      }));
    },

    currentUser: () => {
      // 从localStorage获取当前用户信息
      const { VITE_KZZ_AUTH_USER } = import.meta.env;
      const userStr = localStorage.getItem(VITE_KZZ_AUTH_USER);
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          return user.username || user.name || '';
        } catch (error) {
          console.warn('获取当前用户失败:', error);
          return '';
        }
      }
      return '';
    },
  },

  actions: {
    async fetchUsers() {
      const now = Date.now();
      // 5 分钟缓存
      const cacheTime = 60 * 1000 * 5;
      if (this.users.length > 0 && now - this.usersCacheTime < cacheTime) {
        return this.users;
      }
      const users = await userService.search();
      this.users = users;
      this.usersCacheTime = now;
      return users;
    },
  },
});
