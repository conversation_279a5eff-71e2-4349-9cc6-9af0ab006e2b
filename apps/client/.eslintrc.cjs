/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution');

/** @type { import("eslint").Linter.LegacyConfig } */
const config = {
  root: true,
  ignorePatterns: ['node_modules/'],
  extends: [
    'plugin:vue/essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript/recommended',
    '@vue/eslint-config-prettier',
    'plugin:import/recommended',
    'plugin:import/typescript',
  ],
  settings: {
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts', '.tsx'],
    },
    'import/resolver': {
      // 以下配置依赖 [eslint-import-resolver-typescript](https://github.com/import-js/eslint-import-resolver-typescript#configuration)
      typescript: {
        alwaysTryTypes: true,
        project: ['tsconfig.app.json', 'tsconfig.config.json', 'tsconfig.vitest.json'],
      },
      node: true,
    },
  },
  rules: {
    '@typescript-eslint/no-explicit-any': 'off',
    'vue/multi-word-component-names': 'off',
    // Import
    'import/no-unresolved': 'off',
    'import/namespace': 'off',
    'import/default': 'warn',
    'import/no-named-as-default': 'warn',
    'import/no-named-as-default-member': 'warn',
    'import/order': [
      'error',
      {
        groups: [['builtin', 'external'], ['parent', 'sibling', 'internal', 'index'], 'object', 'unknown'],
        pathGroups: [
          {
            pattern: '**/*.module.less',
            group: 'object',
            position: 'after',
          },
          {
            pattern: '@/**',
            group: 'external',
            position: 'after',
          },
        ],
        distinctGroup: true,
        warnOnUnassignedImports: true,
        'newlines-between': 'always',
      },
    ],
  },
};

module.exports = config;
