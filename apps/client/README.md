# Model Training Platform Client

Frontend application for the Model Training Platform, built with Vue 2.7, TypeScript, and Ant Design Vue.

## Features

- Dataset management interface
- Training job configuration and monitoring
- Model evaluation and comparison tools
- Model deployment management
- User access control

## Technology Stack

- Vue 2.7
- TypeScript
- Ant Design Vue 1.7.8
- Pinia for state management
- Vue Router for navigation
- UnoCSS for styling

## Development

```bash
# Install dependencies
yarn install

# Start development server
yarn dev

# Build for production
yarn build
```

## Project Structure

```
src/
├── assets/         # Static assets
├── components/     # Reusable components
├── pages/          # Page components
├── router/         # Vue Router configuration
├── services/       # API services
├── store/          # Pinia stores
└── utils/          # Utility functions
```
