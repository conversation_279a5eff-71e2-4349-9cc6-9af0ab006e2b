import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import legacy from '@vitejs/plugin-legacy';
import vue2 from '@vitejs/plugin-vue2';
import jsx from '@vitejs/plugin-vue2-jsx';

const plugins = [
  vue2({
    include: [/\.vue$/],
  }),
  jsx(),
  legacy({
    targets: ['chrome >= 87', 'edge >= 88', 'firefox >= 78', 'safari >= 14'],
  }),
  // UnoCSS(),  // 暂时注释掉，稍后修复
  // markdown(),  // 暂时注释掉，稍后修复
];

const alias = {
  '@': fileURLToPath(new URL('./src', import.meta.url)),
};

export default defineConfig({
  base: '/',
  plugins,
  resolve: {
    alias: alias,
  },
  optimizeDeps: {
    exclude: ['vue-demi'],
    force: true,
  },
  build: {
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        math: 'always',
      },
    },
  },
  server: {
    host: true,
    allowedHosts: ['.greatld.com', '.qcc.com'],
    port: 8080,
    proxy: {
      '/api': {
        // target: 'http://p.test.greatld.com',
        target: 'http://localhost:7001',
        changeOrigin: true,
      },
    },
  },
});
