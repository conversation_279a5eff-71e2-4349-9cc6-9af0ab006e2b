docker rm -f test-tp-dd || true
initFile=$PWD/init_sql
echo $initFile

docker run -d \
  --name test-tp-dd \
  --cap-add SYS_NICE \
  -p 3108:3306 \
  -e MYSQL_ROOT_PASSWORD=kezhaozhao_dev \
  -e MYSQL_USER=kezhaozhao_dev \
  -e MYSQL_PASSWORD=kezhaozhao_dev \
  -e MYSQL_DATABASE=model_training_platform_test \
  -e LANG=C.UTF-8 \
  -e MYSQL_SQL_MODE="" \
  -v $initFile:/docker-entrypoint-initdb.d \
  mysql:8.0 \
  --bind-address=0.0.0.0 \
  --default-authentication-plugin=mysql_native_password \
  --character-set-server=utf8mb4 \
  --collation-server=utf8mb4_unicode_ci\
  --sql-mode=""  # 强制覆盖 SQL_MODE



#   # 启动redis
#  docker rm -f test-tp-dd || true
#  docker run -d --name test-tp-dd -p 6381:6379 --rm redis:7.2  --requirepass "yourpassword" --appendonly no

