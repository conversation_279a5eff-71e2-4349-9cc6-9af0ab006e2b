# Model Training Platform Service

Backend service for the Model Training Platform, built with NestJS, TypeScript, and TypeORM.

## Features

- Dataset management API
- Training job orchestration
- Model evaluation services
- Model deployment and monitoring
- User authentication and authorization

## Technology Stack

- NestJS
- TypeScript
- TypeORM with MySQL
- Redis for caching and job queues
- Swagger for API documentation

## Development

```bash
# Install dependencies
yarn install

# Start development server
yarn dev

# Build for production
yarn build

# Run tests
yarn test
```

## Project Structure

```
src/
├── auth/           # Authentication module
├── common/         # Shared utilities and DTOs
├── config/         # Application configuration
├── datasets/       # Dataset management module
├── entities/       # TypeORM entities
├── models/         # Model management module
├── training/       # Training job module
└── users/          # User management module
```
