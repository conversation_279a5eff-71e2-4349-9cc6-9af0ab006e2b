import { Controller, Get } from '@nestjs/common';
import { Public } from 'src/auth/decorators/public.decorator';

@Controller()
export class AppController {
  @Public()
  @Get('health')
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'model-training-platform',
    };
  }

  @Get('protected')
  getProtected() {
    return {
      message: 'This is a protected endpoint',
      timestamp: new Date().toISOString(),
    };
  }
}
