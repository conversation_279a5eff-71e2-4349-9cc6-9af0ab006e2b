export const ExceptionsCode = {
  Common: {
    BadParams: {
      code: 100001,
      error: '参数错误',
    },
    Sample: {
      code: 100002,
      error: '示例错误',
    },
  },
  ProductProjectRelease: {
    ProjectReleaseNotFound: {
      code: 100101,
      error: '项目发版计划不存在',
    },
    ProjectReleaseAlreadyCompleted: {
      code: 100102,
      error: '项目发版计划已经完成，不能进行常规修复, 请创建 Hotfix 修复',
    },
    BranchCanNotBeSame: {
      code: 100103,
      error: '源分支和目标分支不能相同',
    },
    ProductReleaseNotFound: {
      code: 100104,
      error: '产品发版不存在',
    },
    ProjectReleaseNotCompleted: {
      code: 100105,
      error: '项目发版计划未完成，不能进行hotfix修复, 直接补丁发布即可',
    },
    InProgressProductReleaseExist: {
      code: 100106,
      error: '产品发版进行中，不能进行创建',
    },
    BindProjectReleaseFailed: {
      code: 100107,
      error: '绑定项目发版失败',
    },
    SharedProductReleaseNotFound: {
      code: 100108,
      error: '共享的产品发版不存在并且没有自动创建',
    },
    SharedProjectReleaseNotFound: {
      code: 100109,
      error: '共享的项目发版不存在并且没有自动创建',
    },
    ProductReleaseNotPending: {
      code: 100110,
      error: '产品发版不是待发布状态，不能添加项目',
    },
    ProjectReleaseNotPending: {
      code: 100111,
      error: '项目发版计划不是待发布状态，不能取消',
    },
  },
};
