import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ExceptionsCode } from './exceptions.constant';
import { KzzExceptionResponse } from './KzzExceptionResponse';
import { SentryExceptionCaptured } from '@sentry/nestjs';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger: Logger = new Logger(GlobalExceptionFilter.name);

  constructor() {}

  private isHttpStatus(status: number): boolean {
    if (typeof status == 'number' && HttpStatus[status]) {
      return true;
    }
    return false;
  }

  private getStatus(exception: any) {
    if (exception instanceof HttpException) {
      return exception.getStatus();
    }
    if (exception.status && this.isHttpStatus(exception.status)) {
      return exception.status;
    }
    if (exception.statusCode && this.isHttpStatus(exception.statusCode)) {
      return exception.statusCode;
    }
    // @ts-ignore
    if (exception instanceof URIError && exception.statusCode == HttpStatus.BAD_REQUEST) {
      return HttpStatus.BAD_REQUEST;
    }
    return HttpStatus.INTERNAL_SERVER_ERROR;
  }

  @SentryExceptionCaptured()
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const status = this.getStatus(exception);
    const resData = this.formatException(request, exception);
    // this.sentryHelper.captureHttpException(exception, resData);
    // if (process.env.NODE_ENV === 'prod') {
    //   Object.assign(resData, {
    //     message: []
    //   });
    // }
    if (status >= 500) {
      this.logger.error('responseData', JSON.stringify(resData, null, 1));
    }

    // do not modify the original responseData , then sentry will log the original error
    response.status(status).json(resData);
  }

  formatException(request, exception: unknown) {
    const status = this.getStatus(exception);
    let resData: any;
    // sample
    // {
    //   "statusCode": 400,
    //   "error": "Bad Request",
    //   "message": [
    //   {
    //     "target": {
    //       "pageSize": 10,
    //       "pageIndex": null
    //     },
    //     "value": null,
    //     "property": "pageIndex",
    //     "children": [],
    //     "constraints": {
    //       "min": "pageIndex must not be less than 1",
    //       "isNumber": "pageIndex must be a number"
    //     }
    //   }
    // ],
    //   "timestamp": "2019-10-31T02:44:32.856Z",
    //   "path": "/kzz/user/messages?pageSize=10&pageIndex=x"
    // }

    // {
    //   "message": [
    //   "error1"
    // ],
    //   "error": "Internal Server Error",
    //   "statusCode": 500,
    //   "timestamp": "2019-10-31T02:40:06.448Z",
    //   "path": "/kzz/user/error1"
    // }
    if (exception instanceof HttpException) {
      resData = exception.getResponse();
      if (resData.message && !Array.isArray(resData.message)) {
        resData.message = [resData.message];
      }
    } else {
      resData = {
        message: ['Internal server error'],
        error: 'Internal Server Error',
        code: status,
      };
      if (exception instanceof Error) {
        resData.message = [exception.message];
      }
      this.logger.error(exception);
    }
    if (status === 400) {
      resData = Object.assign({}, ExceptionsCode.Common.BadParams, resData);
    }
    const responseData: KzzExceptionResponse = Object.assign({}, resData, {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
    });
    // @ts-ignore
    delete responseData.internalMessage;
    // @ts-ignore
    delete responseData.internalError;
    // @ts-ignore
    delete responseData.errorExtraData;
    return responseData;
  }
}
