import { HttpException } from '@nestjs/common';

export class KzzErrorMessage {
  code: number;
  message?: string | object | any;
  error?: string;
}

export class KzzBaseException extends HttpException {
  constructor(message: KzzErrorMessage, status: number) {
    super(Object.assign(message, { statusCode: status }), status);
  }
}

export class KzzExceptionResponse {
  statusCode: number; // http 状态码
  path: string; // 当前请求的 path
  method: string; // http method
  message?: any[]; // error的详细内容及信息
  code: number; // 错误码
  error: string; // 错误的简单描述
  timestamp: string; // 当前时间
}
