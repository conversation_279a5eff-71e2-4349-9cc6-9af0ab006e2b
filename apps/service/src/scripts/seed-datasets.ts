import { DataSource } from 'typeorm';
import { DatasetSeeder } from '../database/seeds/dataset.seed';
import { DatasetEntity } from '../entities/DatasetEntity';
import { HealthModelDatasetEntity } from '../entities/HealthModelDatasetEntity';
import { RiskModelDatasetEntity } from '../entities/RiskModelDatasetEntity';

async function runSeeds() {
  // 创建数据库连接
  const dataSource = new DataSource({
    type: 'sqlite',
    database: 'database.sqlite',
    entities: [DatasetEntity, HealthModelDatasetEntity, RiskModelDatasetEntity],
    synchronize: true,
    logging: false,
  });

  try {
    await dataSource.initialize();
    console.log('数据库连接成功');

    const seeder = new DatasetSeeder(dataSource);
    await seeder.run();

    console.log('种子数据运行完成！');
  } catch (error) {
    console.error('种子数据运行失败:', error);
  } finally {
    await dataSource.destroy();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runSeeds();
}

export { runSeeds };
