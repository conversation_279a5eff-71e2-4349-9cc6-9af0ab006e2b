import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StorageService } from '../common/storage.service';
import { DatasetEntity, ModelEntity, ModelStatus, TrainingJobEntity } from '../entities';
import { CompareModelsDto } from './dto/compare-models.dto';
import { ModelFiltersDto } from './dto/model-filters.dto';
import { UpdateModelDto } from './dto/update-model.dto';

@Injectable()
export class ModelsService {
  constructor(
    @InjectRepository(ModelEntity)
    private modelRepository: Repository<ModelEntity>,
    @InjectRepository(TrainingJobEntity)
    private trainingJobRepository: Repository<TrainingJobEntity>,
    @InjectRepository(DatasetEntity)
    private datasetRepository: Repository<DatasetEntity>,
    private storageService: StorageService
  ) {}

  /**
   * 查找所有模型
   */
  async findAll(filters: ModelFiltersDto): Promise<[ModelEntity[], number]> {
    const query = this.modelRepository.createQueryBuilder('model');

    if (filters.modelType) {
      query.andWhere('model.modelType = :modelType', { modelType: filters.modelType });
    }

    if (filters.status) {
      query.andWhere('model.status = :status', { status: filters.status });
    }

    if (filters.trainingJobId) {
      query.andWhere('model.trainingJobId = :trainingJobId', { trainingJobId: filters.trainingJobId });
    }

    if (filters.datasetId) {
      query.andWhere('model.datasetId = :datasetId', { datasetId: filters.datasetId });
    }

    if (filters.createdBy) {
      query.andWhere('model.createdBy = :createdBy', { createdBy: filters.createdBy });
    }

    if (filters.search) {
      query.andWhere('(model.name LIKE :search OR model.description LIKE :search)', {
        search: `%${filters.search}%`,
      });
    }

    query.orderBy('model.createdAt', filters.sortOrder || 'DESC');

    if (filters.skip !== undefined) {
      query.skip(filters.skip);
    }

    if (filters.take !== undefined) {
      query.take(filters.take);
    }

    return query.getManyAndCount();
  }

  /**
   * 查找单个模型
   */
  async findOne(id: string): Promise<ModelEntity> {
    const model = await this.modelRepository.findOne({
      where: { id },
      relations: ['trainingJob', 'dataset'],
    });

    if (!model) {
      throw new NotFoundException(`Model with ID ${id} not found`);
    }

    return model;
  }

  /**
   * 更新模型
   */
  async update(id: string, updateModelDto: UpdateModelDto): Promise<ModelEntity> {
    const model = await this.findOne(id);

    // 只允许更新某些字段
    if (updateModelDto.name) model.name = updateModelDto.name;
    if (updateModelDto.description) model.description = updateModelDto.description;
    if (updateModelDto.status) model.status = updateModelDto.status;

    return this.modelRepository.save(model);
  }

  /**
   * 删除模型
   */
  async remove(id: string): Promise<void> {
    const model = await this.findOne(id);

    // 如果模型已经被批准，不允许删除
    if (model.status === ModelStatus.APPROVED) {
      throw new BadRequestException('Cannot delete an approved model');
    }

    // 删除存储中的文件
    try {
      await this.storageService.deleteFile(model.parameters?.traningSampleDataPath);
    } catch (error) {
      console.error('Error deleting model file:', error);
      // 继续删除数据库记录，即使文件删除失败
    }

    await this.modelRepository.remove(model);
  }

  /**
   * 比较模型
   */
  async compareModels(compareModelsDto: CompareModelsDto): Promise<any> {
    const { modelIds } = compareModelsDto;

    // 获取所有模型
    const models = await Promise.all(modelIds.map((id) => this.findOne(id)));

    // 检查所有模型是否属于同一类型
    const modelTypes = new Set(models.map((model) => model.modelType));
    if (modelTypes.size > 1) {
      throw new BadRequestException('Cannot compare models of different types');
    }

    // 提取所有模型的指标
    const comparisonData = models.map((model) => ({
      id: model.id,
      name: model.name,
      version: model.version,
      createdAt: model.createdAt,
      metrics: model.parameters.trainingEngineResponse?.metrics || {},
      parameters: model.parameters || {},
    }));

    // 获取所有指标的名称
    const allMetricNames = new Set<string>();
    comparisonData.forEach((data) => {
      Object.keys(data.metrics).forEach((key) => allMetricNames.add(key));
    });

    // 构建比较结果
    const comparison = {
      modelType: models[0].modelType,
      models: comparisonData,
      metricNames: Array.from(allMetricNames),
    };

    return comparison;
  }
}
