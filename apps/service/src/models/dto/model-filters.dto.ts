import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { ModelStatus } from '../../entities/ModelEntity';
import { SortOrder } from '../../datasets/dto/dataset-filters.dto';
import { ModelTypeEnum } from 'src/common/model-type.enum';

export class ModelFiltersDto {
  @ApiProperty({ description: '模型类型', enum: ModelTypeEnum, required: false })
  @IsEnum(ModelTypeEnum)
  @IsOptional()
  modelType?: ModelTypeEnum;

  @ApiProperty({ description: '模型状态', enum: ModelStatus, required: false })
  @IsEnum(ModelStatus)
  @IsOptional()
  status?: ModelStatus;

  @ApiProperty({ description: '训练任务ID', required: false })
  @IsUUID()
  @IsOptional()
  trainingJobId?: string;

  @ApiProperty({ description: '数据集ID', required: false })
  @IsUUID()
  @IsOptional()
  datasetId?: string;

  @ApiProperty({ description: '创建者ID', required: false })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({ description: '排序顺序', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsEnum(SortOrder)
  @IsOptional()
  sortOrder?: SortOrder;

  @ApiProperty({ description: '跳过记录数', required: false })
  @IsNumber()
  @IsOptional()
  skip?: number;

  @ApiProperty({ description: '获取记录数', required: false })
  @IsNumber()
  @IsOptional()
  take?: number;
}
