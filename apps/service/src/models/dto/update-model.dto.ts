import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';
import { ModelStatus } from '../../entities/ModelEntity';

export class UpdateModelDto {
  @ApiProperty({ description: '模型名称', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  @ApiProperty({ description: '模型描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: '模型状态',
    enum: ModelStatus,
    required: false,
  })
  @IsEnum(ModelStatus)
  @IsOptional()
  status?: ModelStatus;
}
