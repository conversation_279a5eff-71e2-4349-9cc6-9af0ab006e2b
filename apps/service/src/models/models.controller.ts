import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CustomAuthGuard } from 'src/auth/guards/custom-auth.guard';
import { ModelEntity } from '../entities';
import { CompareModelsDto } from './dto/compare-models.dto';
import { ModelFiltersDto } from './dto/model-filters.dto';
import { UpdateModelDto } from './dto/update-model.dto';
import { ModelsService } from './models.service';

@ApiTags('模型管理')
@Controller('models')
@UseGuards(CustomAuthGuard)
@ApiBearerAuth()
export class ModelsController {
  constructor(private readonly modelsService: ModelsService) {}

  @Get()
  @ApiOperation({ summary: '获取模型列表' })
  @ApiResponse({ status: 200, description: '成功获取模型列表' })
  async findAll(@Query() filters: ModelFiltersDto): Promise<{ items: ModelEntity[]; totalCount: number }> {
    const [models, total] = await this.modelsService.findAll(filters);
    return { items: models, totalCount: total };
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个模型详情' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功获取模型详情' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async findOne(@Param('id') id: string): Promise<ModelEntity> {
    return this.modelsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新模型信息' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功更新模型信息' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async update(@Param('id') id: string, @Body() updateModelDto: UpdateModelDto): Promise<ModelEntity> {
    return this.modelsService.update(id, updateModelDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除模型' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功删除模型' })
  @ApiResponse({ status: 400, description: '无法删除已批准的模型' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async remove(@Param('id') id: string): Promise<{ success: boolean }> {
    await this.modelsService.remove(id);
    return { success: true };
  }

  @Post('compare')
  @ApiOperation({ summary: '比较多个模型' })
  @ApiResponse({ status: 200, description: '成功比较模型' })
  @ApiResponse({ status: 400, description: '无法比较不同类型的模型' })
  async compareModels(@Body() compareModelsDto: CompareModelsDto): Promise<any> {
    return this.modelsService.compareModels(compareModelsDto);
  }
}
