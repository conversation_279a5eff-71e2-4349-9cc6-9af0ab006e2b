import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginationResponse } from 'src/common/PaginationResponse';
import { Repository } from 'typeorm';
import { DatasetsService } from '../datasets/datasets.service';
import { JobStatus, ModelEntity, ModelStatus, TrainingJobEntity } from '../entities';
import { CreateTrainingJobDto } from './dto/create-training-job.dto';
import { TrainingJobFiltersDto } from './dto/training-job-filters.dto';
import { UpdateTrainingJobDto } from './dto/update-training-job.dto';
import * as moment from 'moment';
import { StorageService } from 'src/common/storage.service';
import { TrainingParametersModel } from 'src/common/TrainingParametersModel';
import { readFileSync } from 'fs';
import { EngineInnovationHealthService } from './engine-innovation-health.service';
import { TrainResponseDTO } from './dto/engine/TrainResponseDTO';

@Injectable()
export class TrainingService {
  constructor(
    @InjectRepository(TrainingJobEntity)
    private readonly trainingJobRepository: Repository<TrainingJobEntity>,
    @InjectRepository(ModelEntity)
    private readonly modelRepository: Repository<ModelEntity>,
    private readonly datasetsService: DatasetsService,
    private readonly storageService: StorageService,
    private readonly engineInnovationHealthService: EngineInnovationHealthService
  ) {}

  /**
   * 创建训练任务
   */
  async create(createTrainingJobDto: CreateTrainingJobDto, userId: string): Promise<TrainingJobEntity> {
    // 验证数据集是否存在
    const dataset = await this.datasetsService.findOne(createTrainingJobDto.datasetId);

    const params: TrainingParametersModel = {
      version: moment().format('YYYYMMDDHHmmss'),
    };

    // 创建训练任务
    const trainingJob = this.trainingJobRepository.create({
      ...createTrainingJobDto,
      createdBy: userId,
      modelType: dataset.modelType,
      status: JobStatus.PENDING,
      parameters: params,
    });

    return this.trainingJobRepository.save(trainingJob);
  }

  /**
   * 查找所有训练任务
   */
  async findAll(filters: TrainingJobFiltersDto): Promise<PaginationResponse<TrainingJobEntity>> {
    filters.pageIndex = filters.pageIndex || 1;
    filters.pageSize = filters.pageSize || 10;

    const query = this.trainingJobRepository.createQueryBuilder('job');

    if (filters.modelType) {
      query.andWhere('job.modelType = :modelType', { modelType: filters.modelType });
    }

    if (filters.status) {
      query.andWhere('job.status = :status', { status: filters.status });
    }

    if (filters.datasetId) {
      query.andWhere('job.datasetId = :datasetId', { datasetId: filters.datasetId });
    }

    if (filters.createdBy) {
      query.andWhere('job.createdBy = :createdBy', { createdBy: filters.createdBy });
    }

    if (filters.search) {
      query.andWhere('(job.name LIKE :search OR job.description LIKE :search)', {
        search: `%${filters.search}%`,
      });
    }

    query.orderBy('job.createdAt', filters.sortOrder || 'DESC');

    query.skip(filters.pageIndex * filters.pageSize);

    query.take(filters.pageSize);

    const [data, total] = await query.getManyAndCount();
    return {
      data,
      total,
      pageIndex: filters.pageIndex,
      pageSize: filters.pageSize,
    };
  }

  /**
   * 查找单个训练任务
   */
  async findOne(id: string): Promise<TrainingJobEntity> {
    const trainingJob = await this.trainingJobRepository.findOne({ where: { id } });
    if (!trainingJob) {
      throw new NotFoundException(`训练任务 ${id} 不存在`);
    }
    return trainingJob;
  }

  /**
   * 更新训练任务
   */
  async update(id: string, updateTrainingJobDto: UpdateTrainingJobDto): Promise<TrainingJobEntity> {
    const trainingJob = await this.findOne(id);

    // 更新字段
    if (updateTrainingJobDto.name) trainingJob.name = updateTrainingJobDto.name;
    if (updateTrainingJobDto.description) trainingJob.description = updateTrainingJobDto.description;
    // if (updateTrainingJobDto.status) trainingJob.status = updateTrainingJobDto.status;

    // // 更新状态相关字段
    // if (updateTrainingJobDto.status === JobStatus.RUNNING && !trainingJob.startedAt) {
    //   trainingJob.startedAt = new Date();
    // } else if (updateTrainingJobDto.status === JobStatus.COMPLETED && !trainingJob.completedAt) {
    //   trainingJob.completedAt = new Date();

    //   // 如果任务完成，创建模型
    //   if (!trainingJob.modelId) {
    //     const model = await this.createModel(trainingJob);
    //     trainingJob.modelId = model.id;
    //   }
    // }

    return this.trainingJobRepository.save(trainingJob);
  }

  /**
   * 删除训练任务
   */
  async remove(id: string): Promise<void> {
    const trainingJob = await this.findOne(id);

    // 如果任务正在运行，不允许删除
    if (trainingJob.status === JobStatus.RUNNING) {
      throw new BadRequestException('无法删除正在运行的训练任务');
    }

    // 如果任务已完成并创建了模型，不删除模型

    await this.trainingJobRepository.remove(trainingJob);
  }

  /**
   * 启动训练任务
   */
  async startTraining(id: string): Promise<TrainingJobEntity> {
    const trainingJob = await this.findOne(id);

    // 检查任务状态
    if (trainingJob.status !== JobStatus.PENDING) {
      throw new BadRequestException(`无法启动状态为 ${trainingJob.status} 的训练任务`);
    }
    // 更新任务状态
    trainingJob.status = JobStatus.RUNNING;
    trainingJob.startedAt = new Date();
    //1. 先根据dataset 准备数据集并上传到storage
    const dataset = await this.datasetsService.findOne(trainingJob.datasetId);
    const csvFilePath: string = await this.datasetsService.exportDatasetToCsv(dataset);
    const datasetPath = this.storageService.generateDatasetPath(
      dataset.modelType,
      trainingJob.parameters.version,
      `training_data.csv`
    );

    const csvFile = readFileSync(csvFilePath);
    await this.storageService.uploadFile(datasetPath, csvFile);
    trainingJob.parameters.traningSampleDataPath = datasetPath;
    //2. 调用训练引擎启动训练
    const trainResponse: TrainResponseDTO = await this.engineInnovationHealthService.startTraining({
      traningSampleDataPath: datasetPath,
      modelVersion: trainingJob.parameters.version,
      modelId: trainingJob.modelId,
      forceOverwrite: true,
    });
    if (trainResponse.status !== 'success') {
      throw new Error(`Training failed: ${trainResponse.message}`);
    }

    //3. 更新训练任务状态
    trainingJob.status = JobStatus.COMPLETED;
    trainingJob.completedAt = new Date();
    trainingJob.parameters.traningSampleDataPath = datasetPath;
    trainingJob.parameters.trainingEngineResponse = trainResponse;
    //4. 创建模型
    await this.createModel(trainingJob);
    //5. 返回训练任务
    return this.trainingJobRepository.save(trainingJob);
  }

  /**
   * 创建模型, 结合训练平台的训练结果
   */
  private async createModel(trainingJob: TrainingJobEntity): Promise<ModelEntity> {
    // 创建模型
    const model = this.modelRepository.create({
      name: `${trainingJob.name}_model`,
      description: `由训练任务 ${trainingJob.name} 生成的模型`,
      modelType: trainingJob.modelType,
      version: '1.0.0', // 实际应用中应该有版本管理逻辑
      trainingJobId: trainingJob.id,
      datasetId: trainingJob.datasetId,
      parameters: trainingJob.parameters,
      createdBy: trainingJob.createdBy,
      status: ModelStatus.READY,
    });

    return this.modelRepository.save(model);
  }
}
