import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { PaginationQueryParams } from 'src/common/PaginationQueryParams';
import { SortOrder } from '../../datasets/dto/dataset-filters.dto';
import { JobStatus } from '../../entities/TrainingJobEntity';
import { ModelTypeEnum } from 'src/common/model-type.enum';

export class TrainingJobFiltersDto extends PaginationQueryParams {
  @ApiProperty({ description: '模型类型', enum: ModelTypeEnum, required: false })
  @IsEnum(ModelTypeEnum)
  @IsOptional()
  modelType?: ModelTypeEnum;

  @ApiProperty({ description: '训练任务状态', enum: JobStatus, required: false })
  @IsEnum(JobStatus)
  @IsOptional()
  status?: JobStatus;

  @ApiProperty({ description: '数据集ID', required: false })
  @IsUUID()
  @IsOptional()
  datasetId?: string;

  @ApiProperty({ description: '创建者ID', required: false })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({ description: '排序顺序', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsEnum(SortOrder)
  @IsOptional()
  sortOrder?: SortOrder;
}
