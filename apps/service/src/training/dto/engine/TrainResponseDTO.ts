// class TrainingResponse(BaseModel):
//     """训练响应模型"""

//     model_id: str = Field(..., description="模型ID")
//     version: str = Field(..., description="版本号")
//     metrics: Dict[str, float] = Field(..., description="性能指标")
//     training_time: float = Field(..., description="训练时间（秒）")
//     status: str = Field(default="success", description="状态")
//     error: Optional[str] = Field(None, description="错误信息")
//     message: Optional[str] = Field(None, description="响应消息")

export class TrainResponseDTO {
  /**
   * 模型ID
   */
  model_id: string;

  /**
   * 版本号
   */
  version: string;

  /**
   * 性能指标
   */
  metrics: Record<string, number>;

  /**
   * 训练时间（秒）
   */
  training_time: number;

  /**
   * 状态
   */
  status: string = 'success';

  /**
   * 错误信息
   */
  error?: string;

  /**
   * 响应消息
   */
  message?: string;
}
