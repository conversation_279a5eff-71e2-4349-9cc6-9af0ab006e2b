import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModelTypeEnum } from 'src/common/model-type.enum';
import { StartTrainParametersDTO } from './dto/engine/StartTrainParametersDTO';
import { TrainResponseDTO } from './dto/engine/TrainResponseDTO';
import { PredictParametersDTO } from './dto/engine/PredictParametersDTO';
import { PredictResponseDTO } from './dto/engine/PredictResponseDTO';

@Injectable()
export class EngineInnovationHealthService {
  private readonly baseUrl: string;
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {
    this.baseUrl = this.configService.get(`trainingEngine.${ModelTypeEnum.INNOVATION_HEALTH_MODEL}.url`) || '';
  }

  /**
   * 启动训练
   * @param request 训练参数
   * @returns 训练响应
   */
  async startTraining(request: StartTrainParametersDTO): Promise<TrainResponseDTO> {
    const url = `${this.baseUrl}/train`;
    try {
      const response = await this.httpService.axiosRef.post(url, request);
      return response.data as TrainResponseDTO;
    } catch (error) {
      console.error('Training failed:', error);
      throw new Error(`Training failed: ${error.message}`);
    }
  }

  /**
   * 预测
   * @param request 预测参数
   * @returns 预测响应
   */
  async predict(request: PredictParametersDTO): Promise<PredictResponseDTO> {
    const url = `${this.baseUrl}/predict`;
    const response = await this.httpService.axiosRef.post(url, request);
    if (response.status !== 200) {
      throw new Error(`Predict failed: ${response?.statusText}`);
    }
    return response.data as PredictResponseDTO;
  }
}
