import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingJobEntity, ModelEntity } from '../entities';
import { TrainingService } from './training.service';
import { TrainingController } from './training.controller';
import { DatasetsModule } from '../datasets/datasets.module';
import { EngineInnovationHealthService } from './engine-innovation-health.service';
import { StorageService } from 'src/common/storage.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [HttpModule, TypeOrmModule.forFeature([TrainingJobEntity, ModelEntity]), DatasetsModule],
  controllers: [TrainingController],
  providers: [TrainingService, EngineInnovationHealthService, StorageService],
  exports: [TrainingService],
})
export class TrainingModule {}
