import { TestingModule, Test } from '@nestjs/testing';
import { TrainingService } from './training.service';
import { HttpModule } from '@nestjs/axios';
import { getEntityManagerToken, TypeOrmModule } from '@nestjs/typeorm';
import { StorageService } from 'src/common/storage.service';
import { DatasetsModule } from 'src/datasets/datasets.module';
import { TrainingJobEntity, ModelEntity } from 'src/entities';
import { EngineInnovationHealthService } from './engine-innovation-health.service';
import { TrainingController } from './training.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import configuration from 'src/config/configuration';
import { EntityManager } from 'typeorm';
import { TrainResponseDTO } from '.';

describe('TrainingService', () => {
  let service: TrainingService;
  let engineInnovationHealthService: EngineInnovationHealthService;
  let entityManager: EntityManager;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [configuration],
          isGlobal: true,
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          //@ts-ignore
          useFactory: (configService: ConfigService) => configService.get<TypeOrmModuleOptions>('typeorm'),
        }),
        HttpModule,
        TypeOrmModule.forFeature([TrainingJobEntity, ModelEntity]),
        DatasetsModule,
      ],
      controllers: [TrainingController],
      providers: [TrainingService, EngineInnovationHealthService, StorageService],
      exports: [TrainingService],
    }).compile();

    service = module.get<TrainingService>(TrainingService);
    engineInnovationHealthService = module.get<EngineInnovationHealthService>(EngineInnovationHealthService);
    entityManager = module.get<EntityManager>(getEntityManagerToken());
  });

  it('startTraining() test', async () => {
    const trainResponse: TrainResponseDTO = await engineInnovationHealthService.startTraining({
      traningSampleDataPath: 'datasets/innovation_health_model/20250719132121/dataset_training_data.csv',
      modelVersion: '20250719132121',
      modelId: '1234',
      forceOverwrite: false,
    });
    expect(trainResponse).toBeDefined();
  });
});
