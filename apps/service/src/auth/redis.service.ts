import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Injectable()
export class RedisService {
  private readonly client: Redis;
  private readonly TWO_WEEKS = 14 * 24 * 60 * 60; // 14天，单位：秒

  constructor(private readonly configService: ConfigService) {
    const redisConfig = this.configService.get('redis');
    this.client = new Redis({
      ...redisConfig,
      retryStrategy(times) {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
    });
  }

  async set(key: string, value: string, ttl: number = this.TWO_WEEKS): Promise<void> {
    await this.client.set(key, value, 'EX', ttl);
  }

  async get(key: string): Promise<string | null> {
    return this.client.get(key);
  }

  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  async exists(key: string): Promise<boolean> {
    const result = await this.client.exists(key);
    return result === 1;
  }

  getClient(): Redis {
    return this.client;
  }
}
