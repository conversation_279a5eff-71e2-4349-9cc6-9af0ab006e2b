import { Global, Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './controllers/auth.controller';
import { CustomAuthGuard } from './guards/custom-auth.guard';
import { RedisService } from './redis.service';

@Module({
  controllers: [AuthController],
  providers: [AuthService, CustomAuthGuard, RedisService],
  exports: [AuthService, CustomAuthGuard],
  imports: [],
})
@Global()
export class AuthModule {}
