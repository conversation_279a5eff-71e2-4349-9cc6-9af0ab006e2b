import { Test, TestingModule } from '@nestjs/testing';
import { DatasetsService } from './datasets.service';
import { getEntityManagerToken, TypeOrmModule } from '@nestjs/typeorm';
import { StorageService } from 'src/common/storage.service';
import { DatasetEntity, DatasetStatus, HealthModelDatasetEntity, RiskModelDatasetEntity } from 'src/entities';
import { DatasetsController } from './datasets.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import configuration from 'src/config/configuration';
import { EntityManager } from 'typeorm';
import { ModelTypeEnum } from 'src/common/model-type.enum';

describe('DatasetsService', () => {
  let service: DatasetsService;
  let entityManager: EntityManager;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [configuration],
          isGlobal: true,
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          //@ts-ignore
          useFactory: (configService: ConfigService) => configService.get<TypeOrmModuleOptions>('typeorm'),
        }),
        TypeOrmModule.forFeature([DatasetEntity, HealthModelDatasetEntity, RiskModelDatasetEntity]),
      ],
      controllers: [DatasetsController],
      providers: [DatasetsService, StorageService],
      exports: [DatasetsService],
    }).compile();

    service = module.get<DatasetsService>(DatasetsService);
    entityManager = module.get<EntityManager>(getEntityManagerToken());
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('exportDatasetToCsv() test', async () => {
    const dataset: DatasetEntity | null = await entityManager.findOne(DatasetEntity, {
      where: {
        id: 'bda6df7f-11f2-4c25-84e9-32bcb747682c',
      },
    });
    const csvPath = await service.exportDatasetToCsv(dataset);
    console.log(csvPath);
    expect(csvPath).toBeDefined();
  });

  it.skip('create sample datasets', async () => {
    const datasetRepository = entityManager.getRepository(DatasetEntity);
    const healthModelRepository = entityManager.getRepository(HealthModelDatasetEntity);
    const riskModelRepository = entityManager.getRepository(RiskModelDatasetEntity);

    // 创建科创健康性模型数据集
    const healthDataset = await datasetRepository.save({
      name: '科创健康性评估数据集',
      description: '用于评估企业科创健康性的综合数据集，包含技术专利、发展指标、运营指标等多维度数据',
      modelType: ModelTypeEnum.INNOVATION_HEALTH_MODEL,
      createdBy: 'system',
      status: DatasetStatus.READY,
      fileSize: 2048576, // 2MB
      rowCount: 1000,
      columnCount: 25,
      schema: {
        enterprise_id: { type: 'string', description: '企业ID', required: true },
        enterprise_name: { type: 'string', description: '企业名称', required: true },
        comprehensive_score: { type: 'number', description: '综合评分' },
        label: { type: 'integer', description: '标签（0: 健康, 1: 不健康）' },
        tech_patent_application_ratio: { type: 'number', description: '技术专利申请比例' },
        tech_patent_rejection_rate: { type: 'number', description: '技术专利驳回率' },
        tech_patent_authorization_rate: { type: 'number', description: '技术专利授权率' },
        dev_talent_stability: { type: 'number', description: '人才稳定性' },
        dev_equity_financing: { type: 'number', description: '股权融资' },
        oper_capital_paid_ratio: { type: 'number', description: '实缴资本比例' },
        risk_adj_execution_restriction: { type: 'number', description: '执行限制风险调整' },
      },
      tags: ['科创', '健康性', '评估', '示例数据'],
    });

    // 创建内控风险模型数据集
    const riskDataset = await datasetRepository.create({
      name: '内控风险评估数据集',
      description: '企业内控风险评估数据集，包含财务健康状况、合规状态、审计结果等关键风险指标',
      modelType: ModelTypeEnum.GENERIC_RISK_MODEL,
      createdBy: 'system',
      status: DatasetStatus.READY,
      fileSize: 1536000, // 1.5MB
      rowCount: 800,
      columnCount: 8,
      schema: {
        enterprise_id: { type: 'string', description: '企业ID', required: true },
        enterprise_name: { type: 'string', description: '企业名称', required: true },
        risk_score: { type: 'number', description: '风险评分', required: true },
        risk_level: { type: 'string', description: '风险等级', required: true },
        financial_health: { type: 'string', description: '财务健康状况' },
        compliance_status: { type: 'string', description: '合规状态' },
        audit_result: { type: 'string', description: '审计结果' },
      },
      tags: ['内控', '风险', '评估', '示例数据'],
    });

    // 创建科创健康性模型示例数据
    const healthModelData: HealthModelDatasetEntity[] = [];
    for (let i = 1; i <= 50; i++) {
      const entity = healthModelRepository.create({
        datasetId: healthDataset.id,
        enterpriseId: `HEALTH_ENT_${i.toString().padStart(4, '0')}`,
        enterpriseName: `科创企业${i}号`,
        comprehensiveScore: Math.random() * 100,
        label: Math.random() > 0.7 ? 1 : 0, // 30%的概率为不健康
        techPatentApplicationRatio: Math.random() * 0.5,
        techPatentRejectionRate: Math.random() * 0.3,
        techPatentAuthorizationRate: Math.random() * 0.8 + 0.2,
        techPatentMaintenanceRate: Math.random() * 0.9 + 0.1,
        techPatentAuthorizationStability: Math.random() * 0.8,
        techPatentAuthorizationRanking: Math.floor(Math.random() * 100) + 1,
        techSoftwareCopyrightRanking: Math.floor(Math.random() * 100) + 1,
        techPatentConcentration: Math.random() * 0.6,
        techExternalPatentRatio: Math.random() * 0.4,
        techPatentContinuity: Math.random() * 0.9,
        techAdjPatentOutflow: Math.random() * 0.2,
        techAdjPctPatent: Math.random() * 0.3,
        techAdjIpPledge: Math.random() * 0.1,
        techAdjIpTransformation: Math.random() * 0.5,
        techAdjTechAchievement: Math.random() * 0.4,
        devTalentStability: Math.random() * 0.8 + 0.2,
        devEquityFinancing: Math.random() * 1000000,
        devEnterpriseHonor: Math.floor(Math.random() * 10),
        devAdjEmployeeShareholding: Math.random() * 0.3,
        devAdjHonorCancellation: Math.random() * 0.1,
        operCapitalPaidRatio: Math.random() * 0.8 + 0.2,
        operKeyPersonnelChange: Math.random() * 0.3,
        operEquityChangeFrequency: Math.floor(Math.random() * 5),
        operAdjCapitalReduction: Math.random() * 0.1,
        operAdjEquityStructure: Math.random() * 0.2,
        operAdjRelatedPartyChange: Math.random() * 0.15,
        operAdjBusinessStatus: Math.random() * 0.05,
        operAdjRevocation: Math.random() * 0.02,
        riskAdjExecutionRestriction: Math.random() * 0.1,
        riskAdjFinancialLitigation: Math.random() * 0.08,
        riskAdjEnvironmentalPenalty: Math.random() * 0.05,
        riskAdjTaxArrears: Math.random() * 0.03,
      });
      healthModelData.push(entity);
    }

    await healthModelRepository.save(healthModelData);

    // 创建内控风险模型示例数据
    const riskModelData: RiskModelDatasetEntity[] = [];
    const riskLevels = ['低风险', '中风险', '高风险', '极高风险'];
    const financialHealthOptions = ['良好', '一般', '较差', '很差'];
    const complianceStatusOptions = ['合规', '基本合规', '部分违规', '严重违规'];
    const auditResultOptions = ['无保留意见', '保留意见', '否定意见', '无法表示意见'];

    for (let i = 1; i <= 30; i++) {
      const riskScore = Math.random() * 100;
      let riskLevel = '低风险';
      if (riskScore > 75) riskLevel = '极高风险';
      else if (riskScore > 50) riskLevel = '高风险';
      else if (riskScore > 25) riskLevel = '中风险';

      const entity = riskModelRepository.create({
        datasetId: riskDataset.id,
        enterpriseId: `RISK_ENT_${i.toString().padStart(4, '0')}`,
        enterpriseName: `风险评估企业${i}号`,
        riskScore: riskScore,
        riskLevel: riskLevel,
        financialHealth: financialHealthOptions[Math.floor(Math.random() * financialHealthOptions.length)],
        complianceStatus: complianceStatusOptions[Math.floor(Math.random() * complianceStatusOptions.length)],
        auditResult: auditResultOptions[Math.floor(Math.random() * auditResultOptions.length)],
        additionalFields: {
          industry: `行业${Math.floor(Math.random() * 10) + 1}`,
          establishedYear: 2000 + Math.floor(Math.random() * 24),
          employeeCount: Math.floor(Math.random() * 1000) + 50,
          revenue: Math.floor(Math.random() * 100000000) + 1000000,
        },
      });
      riskModelData.push(entity);
    }

    await riskModelRepository.save(riskModelData);

    console.log('数据集种子数据创建完成！');
    console.log(`- 科创健康性数据集: ${healthModelData.length} 条记录`);
    console.log(`- 内控风险数据集: ${riskModelData.length} 条记录`);

    expect(healthDataset).toBeDefined();
    expect(riskDataset).toBeDefined();
    expect(healthModelData.length).toBe(50);
    expect(riskModelData.length).toBe(30);
  });
});
