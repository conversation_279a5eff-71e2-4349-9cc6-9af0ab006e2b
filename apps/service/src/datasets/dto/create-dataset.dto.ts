import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';
import { ModelTypeEnum } from 'src/common/model-type.enum';

export class CreateDatasetDto {
  @ApiProperty({ description: '数据集名称' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: '数据集描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: '模型类型',
    enum: ModelTypeEnum,
    example: ModelTypeEnum.INNOVATION_HEALTH_MODEL,
  })
  @IsEnum(ModelTypeEnum)
  @IsNotEmpty()
  modelType: ModelTypeEnum;

  @ApiProperty({ description: '标签', required: false, type: [String] })
  @IsOptional()
  tags?: string[];
}
