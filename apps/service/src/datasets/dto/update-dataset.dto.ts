import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

export class UpdateDatasetDto {
  @ApiProperty({ description: '数据集名称', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  @ApiProperty({ description: '数据集描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '标签', required: false, type: [String] })
  @IsOptional()
  tags?: string[];
}
