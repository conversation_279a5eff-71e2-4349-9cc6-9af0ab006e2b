import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { DatasetStatus } from '../../entities/DatasetEntity';
import { PaginationQueryParams } from 'src/common/PaginationQueryParams';
import { ModelTypeEnum } from 'src/common/model-type.enum';

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class DatasetFiltersDto extends PaginationQueryParams {
  @ApiProperty({ description: '模型类型', enum: ModelTypeEnum, required: false })
  @IsEnum(ModelTypeEnum)
  @IsOptional()
  modelType?: ModelTypeEnum;

  @ApiProperty({ description: '数据集状态', enum: DatasetStatus, required: false })
  @IsEnum(DatasetStatus)
  @IsOptional()
  status?: DatasetStatus;

  @ApiProperty({ description: '创建者ID', required: false })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({ description: '排序顺序', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsEnum(SortOrder)
  @IsOptional()
  sortOrder?: SortOrder;
}
