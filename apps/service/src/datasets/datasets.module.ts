import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatasetEntity, HealthModelDatasetEntity, RiskModelDatasetEntity } from '../entities';
import { DatasetsService } from './datasets.service';
import { DatasetsController } from './datasets.controller';
import { StorageService } from '../common/storage.service';

@Module({
  imports: [TypeOrmModule.forFeature([DatasetEntity, HealthModelDatasetEntity, RiskModelDatasetEntity])],
  controllers: [DatasetsController],
  providers: [DatasetsService, StorageService],
  exports: [DatasetsService],
})
export class DatasetsModule {}
