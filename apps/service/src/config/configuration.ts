import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { join } from 'path';
import { ModelTypeEnum } from 'src/common/model-type.enum';

export default () => ({
  port: 7001,
  typeorm: {
    type: 'mysql',
    host: process.env.DB_HOST ?? '127.0.0.1',
    port: process.env.DB_PORT ?? 3108,
    username: process.env.DB_USER ?? 'kezhaozhao_dev',
    password: process.env.DB_PASSWD ?? 'kezhaozhao_dev',
    database: process.env.DATABASE ?? 'model_training_platform_test',
    charset: 'utf8mb4',
    synchronize: true, // 设置为true以自动创建实体
    logging: process.env.NODE_ENV === 'development',
    multipleStatements: true,
    entities: [join(__dirname, '../entities/**/*.{js,ts}')],
  } as TypeOrmModuleOptions,
  redis: {
    host: '**************',
    port: 6380,
    db: 6,
    password: '7jt2U_UCREWVTt5pSDgJVrgaR',
    keyPrefix: 'teamPer:',
  },
  storage: {
    rustfs: {
      endpoint: process.env.RUSTFS_ENDPOINT || 'http://**************:6200',
      region: process.env.RUSTFS_REGION || 'us-east-1',
      accessKey: process.env.RUSTFS_ACCESS_KEY || 'kzz',
      secretKey: process.env.RUSTFS_SECRET_KEY || 'kzz_2025',
      bucket: process.env.RUSTFS_BUCKET || 'kzz-model-training-platform',
    },
  },
  trainingEngine: {
    [ModelTypeEnum.INNOVATION_HEALTH_MODEL]: {
      url: process.env.TRAINING_ENGINE_URL || `http://api.test.greatld.com/innovation_health_model/api`,
    },
  },
});
