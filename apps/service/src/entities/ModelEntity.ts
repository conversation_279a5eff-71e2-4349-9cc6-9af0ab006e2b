import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm';
import { TrainingJobEntity } from './TrainingJobEntity';
import { DatasetEntity } from './DatasetEntity';
import { TrainingParametersModel } from 'src/common/TrainingParametersModel';

export enum ModelStatus {
  TRAINING = 'training',
  READY = 'ready',
  APPROVED = 'approved',
  DEPRECATED = 'deprecated',
  ERROR = 'error',
}

@Entity('models')
@Index(['modelType'])
@Index(['status'])
@Index(['createdBy'])
@Index(['trainingJobId'])
export class ModelEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({ name: 'model_type', length: 50 })
  modelType: string;

  @Column({ length: 50 })
  version: string;

  @Column({ name: 'training_job_id', length: 36 })
  trainingJobId: string;

  @ManyToOne(() => TrainingJobEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'training_job_id' })
  trainingJob: TrainingJobEntity;

  @Column({ name: 'dataset_id', length: 36 })
  datasetId: string;

  @ManyToOne(() => DatasetEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'dataset_id' })
  dataset: DatasetEntity;

  @Column('json', { nullable: true })
  parameters: TrainingParametersModel;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by', length: 36 })
  createdBy: string;

  @Column({
    type: 'enum',
    enum: ModelStatus,
    default: ModelStatus.TRAINING,
  })
  status: ModelStatus;
}
