# 模型训练平台设计文档

## 概述

模型训练平台是一个综合性系统，旨在促进机器学习模型的训练、评估和部署。该平台为数据科学家和机器学习工程师提供工具，以简化模型开发生命周期，从数据准备到模型部署和监控。平台设计支持多种模型类型，能够灵活适应不同业务场景的需求。

**核心设计理念**：

- **多模型类型支持**：平台能够管理不同类型的模型（如科创健康性模型、内控风险模型等）
- **灵活的数据存储**：采用关系型数据库存储元数据，专用表存储不同类型的数据集内容
- **可扩展架构**：支持新增模型类型和训练引擎，无需修改核心代码
- **统一界面**：为所有模型类型提供一致的用户体验
- **安全可靠**：确保数据安全和系统稳定性
- **权限管理**：提供细粒度的用户权限控制，确保数据和模型的安全访问

设计目标：

- 为数据科学家提供直观、可选代码的体验
- 确保整个机器学习生命周期的可追溯性和版本控制
- 提供训练任务和已部署模型的实时监控和反馈
- 支持多用户并发使用，具有适当的访问控制
- 实现与多种训练引擎的集成
- 即使处理大型数据集和复杂模型也能保持高性能
- 支持系统资源监控和告警机制

## 架构

### 系统架构

```mermaid
graph TB
    subgraph "前端层"
        UI[Web界面]
        Mobile[移动端适配]
    end

    subgraph "API网关层"
        Gateway[API网关]
        Auth[认证授权]
        RateLimit[限流控制]
    end

    subgraph "应用服务层"
        DataService[数据集服务]
        TrainingService[训练服务]
        ModelService[模型管理]
        DeploymentService[部署服务]
        MonitoringService[监控服务]
    end

    subgraph "训练引擎层"
        HealthEngine[科创健康性模型引擎]
        RiskEngine[内控风险模型引擎]
        FutureEngine[未来可扩展引擎...]
    end

    subgraph "存储层"
        MySQL[(MySQL数据库)]
        FileStorage[(本地文件存储)]
    end

    UI --> Gateway
    Mobile --> Gateway
    Gateway --> Auth
    Auth --> DataService
    Auth --> TrainingService
    Auth --> ModelService
    Auth --> DeploymentService
    Auth --> MonitoringService

    DataService --> MySQL
    TrainingService --> MySQL
    ModelService --> MySQL
    DeploymentService --> MySQL
    MonitoringService --> MySQL

    DataService --> FileStorage
    ModelService --> FileStorage

    TrainingService --> HealthEngine
    TrainingService --> RiskEngine
    TrainingService --> FutureEngine

    DeploymentService --> HealthEngine
    DeploymentService --> RiskEngine
    DeploymentService --> FutureEngine
```

### 技术栈

**后端框架**: NestJS + TypeScript

- 理由：提供结构化、模块化的架构，内置依赖注入
- 支持TypeScript，提高类型安全性和开发体验
- 与各种数据库和其他服务的出色集成

**前端框架**: Vue 3 + TypeScript + AntDesign

- 理由：提供现代化、基于组件的架构，支持Composition API
- 强大的TypeScript支持，提高可维护性
- 优秀的生态系统，包括UI组件、状态管理和路由库

**数据库**: MySQL

- 理由：成熟稳定的关系型数据库，支持复杂查询和事务
- 良好的性能和可扩展性
- 广泛的社区支持和工具生态系统

**文件存储**: RUSTFS

- 理由：简化部署和配置
- 适合开发和小规模部署环境

**容器化**: Docker + Kubernetes

- 理由：标准化部署环境，简化运维
- 支持服务的水平扩展和自动恢复
- 提供灵活的资源管理和服务发现

## 组件和接口

### 1. 数据集服务

**职责**: 管理数据集的上传、存储、验证和预处理

**核心接口**:

```typescript
@Injectable()
export class DatasetService {
  async uploadDataset(file: Express.Multer.File, metadata: DatasetMetadata): Promise<Dataset>;
  async listDatasets(filters: DatasetFilters): Promise<PaginatedResponse<Dataset>>;
  async getDatasetById(id: string): Promise<Dataset>;
  async getDatasetPreview(id: string, limit: number = 100): Promise<DatasetPreview>;
  async deleteDataset(id: string): Promise<void>;
  async validateDataset(id: string, modelType: string): Promise<ValidationResult>;
  async getDatasetSchema(modelType: string): Promise<SchemaDefinition>;
}
```

**设计决策**:

- 使用流式处理大文件上传
- 实现基于模型类型的数据验证
- 在MySQL中存储元数据，在专用表中存储具体数据
- 支持数据集版本控制，确保可重现性
- 在数据集级别实现访问控制

### 2. 训练服务

**职责**: 配置、执行和监控模型训练任务

**核心接口**:

```typescript
@Injectable()
export class TrainingService {
  async createTrainingJob(config: TrainingJobConfig): Promise<TrainingJob>;
  async listTrainingJobs(filters: JobFilters): Promise<PaginatedResponse<TrainingJob>>;
  async getTrainingJobById(id: string): Promise<TrainingJob>;
  async startTrainingJob(id: string): Promise<void>;
  async stopTrainingJob(id: string): Promise<void>;
  async getTrainingLogs(id: string, options: LogOptions): Promise<LogEntry[]>;
  async getTrainingMetrics(id: string): Promise<TrainingMetrics>;
  async getTrainingEngineConfig(modelType: string): Promise<EngineConfig>;
}
```

**设计决策**:

- 基于模型类型选择适当的训练引擎
- 实现训练任务的异步处理
- 支持实时日志和指标收集
- 为训练任务启用资源分配和约束
- 实现任务优先级和调度

### 3. 模型管理服务

**职责**: 存储、版本化和管理训练好的模型及其元数据

**核心接口**:

```typescript
@Injectable()
export class ModelService {
  async registerModel(model: ModelRegistration): Promise<Model>;
  async listModels(filters: ModelFilters): Promise<PaginatedResponse<Model>>;
  async getModelById(id: string): Promise<Model>;
  async compareModels(ids: string[]): Promise<ModelComparison>;
  async downloadModel(id: string, format: ExportFormat): Promise<StreamableFile>;
  async deleteModel(id: string): Promise<void>;
  async getModelTypeMetrics(modelType: string): Promise<MetricDefinitions>;
}
```

**设计决策**:

- 实现模型的语义化版本控制
- 在本地文件系统中存储模型文件，在MySQL中存储元数据
- 支持跨多个指标的模型比较
- 启用模型血统跟踪（数据集、训练任务、超参数）
- 实现模型审批工作流，用于生产部署

### 4. 部署服务

**职责**: 将模型部署到生产环境并管理其生命周期

**核心接口**:

```typescript
@Injectable()
export class DeploymentService {
  async deployModel(modelId: string, config: DeploymentConfig): Promise<Deployment>;
  async listDeployments(filters: DeploymentFilters): Promise<PaginatedResponse<Deployment>>;
  async getDeploymentById(id: string): Promise<Deployment>;
  async updateDeployment(id: string, updates: DeploymentUpdates): Promise<Deployment>;
  async rollbackDeployment(id: string, version: string): Promise<Deployment>;
  async deleteDeployment(id: string): Promise<void>;
  async testDeployment(id: string, input: Record<string, any>): Promise<PredictionResult>;
  async getDeploymentEngineConfig(modelType: string): Promise<EngineConfig>;
}
```

**设计决策**:

- 基于模型类型选择适当的推理引擎
- 实现版本控制和回滚机制
- 支持A/B测试和金丝雀部署
- 为已部署模型实现请求日志和监控
- 提供预测解释功能

### 5. 监控服务

**职责**: 监控训练任务和已部署模型的性能和健康状况，以及系统资源使用情况

**核心接口**:

```typescript
@Injectable()
export class MonitoringService {
  async getSystemStatus(): Promise<SystemStatus>;
  async getResourceUsage(resourceType: ResourceType): Promise<ResourceUsage>;
  async getModelPerformance(deploymentId: string, timeRange: TimeRange): Promise<PerformanceMetrics>;
  async createAlert(config: AlertConfig): Promise<Alert>;
  async listAlerts(filters: AlertFilters): Promise<PaginatedResponse<Alert>>;
  async acknowledgeAlert(id: string): Promise<void>;
  async getSystemLogs(filters: LogFilters): Promise<LogEntry[]>;
  async configureMonitoring(settings: MonitoringSettings): Promise<void>;
}
```

**设计决策**:

- 实现实时监控和告警
- 支持自定义仪表板和报告
- 为不同模型类型提供特定的性能指标
- 实现数据漂移检测
- 支持资源使用监控和优化建议
- 提供详细的系统活动日志记录和查询
- 支持可配置的监控阈值和告警触发条件
- 实现多种通知渠道（电子邮件、企业微信等）

## 数据模型

### 核心数据模型

```typescript
// 数据集元数据模型
interface Dataset {
  id: string;
  name: string;
  description: string;
  modelType: string; // 模型类型，如"科创健康性模型"
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  fileSize: number;
  rowCount: number;
  columnCount: number;
  schema: DatasetSchema;
  tags: string[];
  status: DatasetStatus;
}

// 训练任务模型
interface TrainingJob {
  id: string;
  name: string;
  description: string;
  datasetId: string;
  modelType: string; // 模型类型
  engineId: string; // 训练引擎ID
  parameters: Record<string, any>;
  status: JobStatus;
  createdAt: Date;
  startedAt: Date | null;
  completedAt: Date | null;
  createdBy: string;
  metrics: Record<string, number>;
  logs: string[];
  modelId: string | null;
}

// 模型模型
interface Model {
  id: string;
  name: string;
  description: string;
  modelType: string; // 模型类型
  version: string;
  trainingJobId: string;
  datasetId: string;
  metrics: Record<string, number>;
  parameters: Record<string, any>;
  filePath: string;
  createdAt: Date;
  createdBy: string;
  status: ModelStatus;
  approvedBy: string | null;
  approvedAt: Date | null;
}

// 部署模型
interface Deployment {
  id: string;
  name: string;
  description: string;
  modelId: string;
  modelType: string; // 模型类型
  engineId: string; // 推理引擎ID
  endpoint: string;
  version: string;
  status: DeploymentStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  metrics: Record<string, number>;
}

// 训练引擎配置
interface EngineConfig {
  id: string;
  name: string;
  modelType: string; // 支持的模型类型
  apiUrl: string; // API端点
  apiKey: string; // API密钥
  parameters: Record<string, any>; // 引擎特定参数
  status: EngineStatus;
  createdAt: Date;
  updatedAt: Date;
}
```

### 数据库设计

#### 数据集存储设计

```sql
-- 数据集元数据表
CREATE TABLE datasets (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  model_type VARCHAR(50) NOT NULL,  -- 模型类型
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  created_by VARCHAR(36) NOT NULL,
  file_size BIGINT NOT NULL,
  row_count INT NOT NULL,
  column_count INT NOT NULL,
  schema JSON NOT NULL,
  tags JSON,
  status VARCHAR(20) NOT NULL,
  INDEX (model_type),
  INDEX (created_by),
  INDEX (status)
);

-- 科创健康性模型数据集表
CREATE TABLE health_model_datasets (
  id VARCHAR(36) PRIMARY KEY,
  dataset_id VARCHAR(36) NOT NULL,  -- 关联到datasets表
  company_name VARCHAR(255),
  registration_capital DECIMAL(20, 2),
  establishment_date DATE,
  industry_code VARCHAR(20),
  employee_count INT,
  patent_count INT,
  -- 其他特定字段...
  FOREIGN KEY (dataset_id) REFERENCES datasets(id) ON DELETE CASCADE,
  INDEX (dataset_id)
);

-- 内控风险模型数据集表
CREATE TABLE risk_model_datasets (
  id VARCHAR(36) PRIMARY KEY,
  dataset_id VARCHAR(36) NOT NULL,  -- 关联到datasets表
  company_id VARCHAR(36),
  risk_score DECIMAL(5, 2),
  financial_health VARCHAR(20),
  compliance_status VARCHAR(20),
  audit_result VARCHAR(50),
  -- 其他特定字段...
  FOREIGN KEY (dataset_id) REFERENCES datasets(id) ON DELETE CASCADE,
  INDEX (dataset_id)
);

-- 可以根据需要添加更多模型类型的数据集表
```

**设计决策**:

- 使用主表存储所有数据集的通用元数据
- 为每种模型类型创建专用表存储具体数据
- 通过外键关联确保数据完整性
- 使用索引优化查询性能
- 支持JSON字段存储灵活的模式信息

## 错误处理

### 错误分类和处理策略

**1. 用户输入错误**

- 数据验证失败：返回详细的验证错误信息，包括字段和错误原因
- 权限问题：返回403状态码和清晰的权限要求说明
- 资源未找到：返回404状态码和可能的替代资源建议

**2. 系统错误**

- 训练引擎通信失败：记录详细日志，实现重试机制，返回用户友好的错误信息
- 数据库操作失败：实现事务回滚，记录错误上下文，返回适当的错误代码
- 文件操作错误：处理存储限制和权限问题，提供明确的错误指导

**3. 业务逻辑错误**

- 模型训练失败：记录训练日志，保存中间状态，提供详细的失败原因
- 部署冲突：实现版本控制和冲突解决策略，提供明确的解决选项
- 数据一致性问题：实现数据验证和修复机制，记录不一致的情况

### 错误响应格式

```typescript
interface ErrorResponse {
  code: string; // 错误代码
  message: string; // 用户友好的错误消息
  details?: any; // 详细错误信息
  timestamp: string; // 错误发生时间
  traceId: string; // 用于跟踪的唯一ID
  suggestions?: string[]; // 可能的解决方案
}
```

## 测试策略

### 测试层次

**1. 单元测试**

- 覆盖所有核心业务逻辑
- 使用Jest框架和模拟对象
- 目标代码覆盖率：80%以上
- 重点测试不同模型类型的处理逻辑

**2. 集成测试**

- 测试API接口的完整流程
- 验证与训练引擎的通信
- 测试数据库操作和文件存储
- 验证不同模型类型的端到端流程

**3. 性能测试**

- 测试大数据集的上传和处理性能
- 验证并发训练任务的处理能力
- 测试模型部署和预测的响应时间
- 监控系统资源使用情况

### 测试数据管理

- 为每种模型类型创建测试数据集
- 实现测试数据的自动生成和清理
- 使用Docker容器隔离测试环境
- 支持持续集成环境中的自动化测试

## 部署和配置

### 部署架构

**开发环境**: Docker Compose单机部署

- MySQL数据库
- NestJS后端服务
- Vue前端应用
- 本地文件存储

**生产环境**: Kubernetes集群部署

- 高可用MySQL集群
- 水平扩展的API服务
- 静态资源CDN
- 共享文件存储（可扩展到对象存储）

### 配置管理

**配置项**:

```typescript
interface AppConfig {
  // 数据库配置
  database: {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
  };

  // 文件存储配置
  storage: {
    type: 'local' | 'oss';
    basePath: string;
    tempPath: string;
  };

  // 训练引擎配置
  engines: {
    [modelType: string]: {
      apiUrl: string;
      apiKey: string;
      timeout: number;
    };
  };

  // 安全配置
  security: {
    jwtSecret: string;
    jwtExpiration: number;
    bcryptRounds: number;
  };

  // 系统配置
  system: {
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    port: number;
    maxUploadSize: number;
  };
}
```

## 安全考虑

**1. 认证和授权**

- 实现基于Session的认证机制（与现有项目保持一致，代码位于apps/service/src/auth）
- 实现简单的访问控制，优先保证系统可用性
- 实现会话管理和自动登出
- 后续可扩展更复杂的权限管理

**2. 数据安全**

- 敏感数据加密存储
- 传输数据使用HTTPS
- 实现数据访问审计日志
- 支持数据备份和恢复

**3. API安全**

- 实现请求限流和防DDoS措施
- API密钥管理和轮换
- 输入验证和防注入
- CORS配置和安全头部

## 扩展性考虑

**1. 新增模型类型**

- 定义新模型类型的数据模式
- 创建对应的数据集表
- 配置训练引擎连接
- 添加特定的性能指标和可视化

**2. 集成新训练引擎**

- 实现训练引擎适配器
- 配置API端点和认证
- 定义参数映射和结果转换
- 添加健康检查和监控

**3. 扩展存储选项**

- 实现存储提供者接口
- 添加对象存储支持
- 配置文件迁移和同步
- 优化大文件处理性能
